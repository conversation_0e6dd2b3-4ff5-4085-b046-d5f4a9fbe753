/**
 * Integration Manager
 * Handles Telegram and Discord integration UI and manual sending
 */
import { BaseManager } from '../core/BaseManager.js';
import {
    getTelegramSettings,
    isTelegramConfigured
} from '../../../js/user/telegramSettings.js';
import {
    getDiscordSettings,
    isDiscordConfigured
} from '../../../js/user/discordSettings.js';
import {
    sendAnalysisToTelegram,
    testTelegramConnection
} from '../../../js/integrations/telegram.js';
import {
    sendAnalysisToDiscord,
    testDiscordWebhook
} from '../../../js/integrations/discord.js';

export class IntegrationManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
    }

    /**
     * Send analysis to Telegram manually
     */
    async sendToTelegram(analysisId) {
        try {
            const analysisItem = await this.controller.dataManager.getAnalysisById(analysisId);
            if (!analysisItem) {
                this.controller.uiManager.showError('Analysis not found');
                return;
            }

            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: new Date(analysisItem.date)
            };

            // This would typically get settings and send
            // For now, showing a placeholder implementation
            this.controller.uiManager.showSuccess('Telegram send functionality would be implemented here');
            
        } catch (error) {
            console.error('Error sending to Telegram:', error);
            this.controller.uiManager.showError('Failed to send to Telegram');
        }
    }

    /**
     * Send analysis to Discord manually
     */
    async sendToDiscord(analysisId) {
        try {
            const analysisItem = await this.controller.dataManager.getAnalysisById(analysisId);
            if (!analysisItem) {
                this.controller.uiManager.showError('Analysis not found');
                return;
            }

            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: new Date(analysisItem.date)
            };

            // This would typically get settings and send
            // For now, showing a placeholder implementation
            this.controller.uiManager.showSuccess('Discord send functionality would be implemented here');
            
        } catch (error) {
            console.error('Error sending to Discord:', error);
            this.controller.uiManager.showError('Failed to send to Discord');
        }
    }

    /**
     * Send analysis to Telegram
     */
    async sendAnalysisToTelegram(analysisId) {
        try {
            // Get the analysis data
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (!analysisItem) {
                this.controller.uiManager.showError('Could not find the selected analysis.');
                return;
            }

            // Get Telegram settings
            const settings = await getTelegramSettings();
            if (!settings) {
                this.controller.uiManager.showError('Telegram not configured. Please configure Telegram in Settings.');
                return;
            }

            // Show loading state
            const telegramBtn = document.querySelector(`.telegram-send-btn[data-analysis-id="${analysisId}"]`);
            if (telegramBtn) {
                telegramBtn.disabled = true;
                telegramBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }

            // Prepare analysis data for sending
            const analysisData = {
                type: analysisItem.analysisType,
                result: analysisItem.result,
                date: analysisItem.date
            };

            // Send to Telegram
            const result = await sendAnalysisToTelegram(analysisData, settings.botToken, settings.chatId);

            if (result.success) {
                this.controller.uiManager.showSuccess(`Analysis sent to Telegram successfully! ${result.messageCount > 1 ? `(${result.messageCount} messages)` : ''}`);
            } else {
                this.controller.uiManager.showError(`Failed to send to Telegram: ${result.error}`);
            }

        } catch (error) {
            console.error('Error sending analysis to Telegram:', error);
            this.controller.uiManager.showError('Failed to send analysis to Telegram');
        } finally {
            // Restore button state
            const telegramBtn = document.querySelector(`.telegram-send-btn[data-analysis-id="${analysisId}"]`);
            if (telegramBtn) {
                telegramBtn.disabled = false;
                telegramBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 3L3 10.5L10.5 13.5L13.5 21L21 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.5 13.5L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }
        }
    }

    /**
     * Send analysis to Discord
     */
    async sendAnalysisToDiscord(analysisId) {
        try {
            console.log('💬 IntegrationManager: Starting Discord send for analysis:', analysisId);

            // Get the analysis data
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (!analysisItem) {
                console.error('❌ Analysis not found:', analysisId);
                this.controller.uiManager.showError('Could not find the selected analysis.');
                return;
            }

            console.log('✅ Analysis found:', analysisItem.analysisType);

            // Get Discord settings
            const settings = await getDiscordSettings();
            if (!settings) {
                console.error('❌ Discord not configured');
                this.controller.uiManager.showError('Discord not configured. Please configure Discord in Settings.');
                return;
            }

            console.log('✅ Discord settings found, webhook URL length:', settings.webhookUrl?.length || 0);

            // Show loading state
            const discordBtn = document.querySelector(`.discord-send-btn[data-analysis-id="${analysisId}"]`);
            if (discordBtn) {
                discordBtn.disabled = true;
                discordBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }

            // Prepare analysis data for sending
            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: analysisItem.date
            };

            console.log('📋 Analysis data prepared:', {
                analysisType: analysisData.analysisType,
                resultType: typeof analysisData.result,
                resultContent: typeof analysisData.result === 'string' ?
                    analysisData.result.substring(0, 100) + '...' :
                    (analysisData.result?.content ? analysisData.result.content.substring(0, 100) + '...' : 'No content'),
                date: analysisData.date
            });

            console.log('📤 Sending to Discord:', analysisData.type);

            // Send to Discord
            const result = await sendAnalysisToDiscord(analysisData, settings.webhookUrl);

            console.log('📨 Discord send result:', result);

            if (result.success) {
                console.log('✅ Discord send successful');
                this.controller.uiManager.showSuccess('Analysis sent to Discord successfully!');
            } else {
                console.error('❌ Discord send failed:', result.error);
                this.controller.uiManager.showError(`Failed to send to Discord: ${result.error}`);
            }

        } catch (error) {
            console.error('Error sending analysis to Discord:', error);
            this.controller.uiManager.showError('Failed to send analysis to Discord');
        } finally {
            // Restore button state
            const discordBtn = document.querySelector(`.discord-send-btn[data-analysis-id="${analysisId}"]`);
            if (discordBtn) {
                discordBtn.disabled = false;
                discordBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                `;
            }
        }
    }
}
