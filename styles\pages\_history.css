.history-item {
    background: #1D1A2A;
    border: 1px solid #2C2738;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 160px; /* Ensure cards have minimum height to use more space */
    display: flex;
    flex-direction: column;
}

.history-item:hover {
    transform: translateY(-2px);
    background: #252133;
    border-color: #5BA9F9;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 16px;
}

.history-item-title {
    font-weight: 600;
    color: #F9F9F9;
    font-size: 15px;
    flex: 1;
    line-height: 1.3;
}

.history-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    flex-shrink: 0;
}

.history-item-date {
    font-size: 12px;
    color: #B2AFC5;
    white-space: nowrap;
}

.history-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.delete-btn {
    background: none;
    border: none;
    color: #B2AFC5;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.delete-btn:hover {
    color: #FF5C5C;
    background: rgba(255, 92, 92, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

.delete-btn svg {
    transition: all 0.2s ease;
}

.history-item-summary {
    font-size: 14px;
    color: #B2AFC5;
    line-height: 1.5;
    flex: 1; /* Allow summary to grow and fill available space */
    
    /* Truncate text after 6 lines to use more space */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 6; /* increased from 3 to 6 lines */
    -webkit-box-orient: vertical;
    margin-bottom: 16px; /* Add more space before footer */
}

.history-item-footer {
    margin-top: 12px;
    text-align: right;
}

.view-details-link {
    font-size: 13px;
    font-weight: 600;
    color: #5BA9F9;
    text-decoration: none;
    transition: color 0.3s ease;
}

.history-item:hover .view-details-link {
    color: #FFD84D;
}

.history-grid .history-item {
    margin-bottom: 0; /* Remove bottom margin as gap is used */
}

.history-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

    .history-item-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .history-item-meta {
        align-items: flex-start;
        width: 100%;
    }

    .history-item-date {
        margin-bottom: 4px;
    }

    .history-item-actions {
        justify-content: flex-start;
    }
