
/* API Status and Miscellaneous Components */
.api-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #1D1A2A;
    border-radius: 10px;
    border: 1px solid #2C2738;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF5C5C;
    animation: blink 2s infinite;
}

.status-indicator.connected {
    background: #28a745;
    animation: none;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.status-text {
    color: #B2AFC5;
    font-size: 14px;
}

/* Results Styles */
.results-container {
    max-height: none;
    overflow-y: visible;
    padding: 20px;
    background: #1D1A2A;
    border-radius: 12px;
    border: 2px solid #2C2738;
    margin-bottom: 16px;
    color: #F9F9F9;
    line-height: 1.6;
}

.results-container::-webkit-scrollbar {
    width: 6px;
}

.results-container::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb:hover {
    background: #4A9AE8;
}

.results-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* Loading Styles */
.loading-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #2C2738;
    border-top: 4px solid #5BA9F9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text h3 {
    color: #F9F9F9;
    margin-bottom: 8px;
    font-size: 18px;
}

.loading-text p {
    color: #B2AFC5;
    margin-bottom: 20px;
}

.loading-progress {
    max-width: 200px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #2C2738;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #5BA9F9, #FFD84D);
    border-radius: 3px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Action Icons */
.action-icon.manage-prompts {
    background: linear-gradient(135deg, #FF6B6B 0%, #FFD93D 100%);
}

/* Pro Feature Section Styling */
.pro-feature-section {
    position: relative;
}

.pro-feature-section::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(145deg, rgba(168, 192, 255, 0.2), rgba(63, 43, 150, 0.2));
    border-radius: 13px;
    z-index: -1;
}
