// Telegram Settings Management (Pro Feature)
import { checkProStatus } from './proStatus.js';
import { validateBotToken, validateChatId } from '../integrations/telegram.js';
import { AUTO_SEND_CONFIG } from '../../config.js';

/**
 * Save Telegram settings (Pro users only)
 * @param {string} botToken - Telegram bot token
 * @param {string} chatId - Telegram chat ID
 * @returns {Promise<Object>} - Save result
 */
export async function saveTelegramSettings(botToken, chatId) {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return {
                success: false,
                error: 'Telegram integration is a Pro feature. Please upgrade to use this functionality.'
            };
        }
        
        // Validate inputs
        if (!botToken || !chatId) {
            return {
                success: false,
                error: 'Both bot token and chat ID are required'
            };
        }
        
        const trimmedToken = botToken.trim();
        const trimmedChatId = chatId.trim();
        
        if (!validateBotToken(trimmedToken)) {
            return {
                success: false,
                error: 'Invalid bot token format. Expected format: 123456789:ABC-DEF1234ghIkl-zyx57W2v1u123ew11'
            };
        }
        
        if (!validateChatId(trimmedChatId)) {
            return {
                success: false,
                error: 'Invalid chat ID format. Expected format: 123456789 or @username'
            };
        }
        
        // Save settings
        await chrome.storage.sync.set({
            telegramSettings: {
                botToken: trimmedToken,
                chatId: trimmedChatId,
                savedAt: new Date().toISOString()
            }
        });
        
        return {
            success: true,
            message: 'Telegram settings saved successfully!'
        };
        
    } catch (error) {
        console.error('Error saving Telegram settings:', error);
        return {
            success: false,
            error: `Failed to save settings: ${error.message}`
        };
    }
}

/**
 * Get Telegram settings (Pro users only)
 * @returns {Promise<Object>} - Settings or null if not configured
 */
export async function getTelegramSettings() {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return null;
        }
        
        const result = await chrome.storage.sync.get(['telegramSettings']);
        return result.telegramSettings || null;
        
    } catch (error) {
        console.error('Error getting Telegram settings:', error);
        return null;
    }
}

/**
 * Clear Telegram settings
 * @returns {Promise<boolean>} - Success status
 */
export async function clearTelegramSettings() {
    try {
        await chrome.storage.sync.remove(['telegramSettings', AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND]);
        return true;
    } catch (error) {
        console.error('Error clearing Telegram settings:', error);
        return false;
    }
}

/**
 * Check if Telegram is configured for current user
 * @returns {Promise<boolean>} - Whether Telegram is configured
 */
export async function isTelegramConfigured() {
    const settings = await getTelegramSettings();
    return !!(settings && settings.botToken && settings.chatId);
}

/**
 * Get masked bot token for display
 * @returns {Promise<string>} - Masked token or empty string
 */
export async function getMaskedBotToken() {
    try {
        const settings = await getTelegramSettings();
        if (!settings || !settings.botToken) {
            return '';
        }
        
        const token = settings.botToken;
        const colonIndex = token.indexOf(':');
        
        if (colonIndex === -1) {
            return '*'.repeat(token.length);
        }
        
        const botId = token.substring(0, colonIndex);
        const tokenPart = token.substring(colonIndex + 1);
        
        if (tokenPart.length <= 8) {
            return `${botId}:${'*'.repeat(tokenPart.length)}`;
        }
        
        return `${botId}:${tokenPart.substring(0, 4)}${'*'.repeat(tokenPart.length - 8)}${tokenPart.substring(tokenPart.length - 4)}`;
        
    } catch (error) {
        console.error('Error getting masked bot token:', error);
        return '';
    }
}

/**
 * Validate current Telegram configuration
 * @returns {Promise<Object>} - Validation result
 */
export async function validateTelegramConfig() {
    try {
        const settings = await getTelegramSettings();
        
        if (!settings) {
            return {
                isValid: false,
                error: 'No Telegram settings found'
            };
        }
        
        if (!settings.botToken || !settings.chatId) {
            return {
                isValid: false,
                error: 'Incomplete Telegram configuration'
            };
        }
        
        if (!validateBotToken(settings.botToken)) {
            return {
                isValid: false,
                error: 'Invalid bot token format'
            };
        }
        
        if (!validateChatId(settings.chatId)) {
            return {
                isValid: false,
                error: 'Invalid chat ID format'
            };
        }
        
        return {
            isValid: true,
            settings: settings
        };
        
    } catch (error) {
        console.error('Error validating Telegram config:', error);
        return {
            isValid: false,
            error: `Validation failed: ${error.message}`
        };
    }
}

/**
 * Save Telegram auto-send settings (Pro users only)
 * @param {boolean} enabled - Whether auto-send is enabled
 * @returns {Promise<Object>} - Save result
 */
export async function saveTelegramAutoSendSettings(enabled) {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return {
                success: false,
                error: 'Auto-send is a Pro feature. Please upgrade to use this functionality.'
            };
        }
        
        // Check if Telegram is configured
        const isConfigured = await isTelegramConfigured();
        if (!isConfigured && enabled) {
            return {
                success: false,
                error: 'Please configure Telegram settings before enabling auto-send.'
            };
        }
        
        const currentSettings = await getTelegramAutoSendSettings();
        const newSettings = {
            ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS,
            ...currentSettings,
            enabled: !!enabled,
            updatedAt: new Date().toISOString()
        };
        
        // Reset failure count when enabling
        if (enabled && !currentSettings.enabled) {
            newSettings.failureCount = 0;
        }
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND]: newSettings
        });
        
        return {
            success: true,
            message: `Telegram auto-send ${enabled ? 'enabled' : 'disabled'} successfully!`
        };
        
    } catch (error) {
        console.error('Error saving Telegram auto-send settings:', error);
        return {
            success: false,
            error: `Failed to save auto-send settings: ${error.message}`
        };
    }
}

/**
 * Get Telegram auto-send settings (Pro users only)
 * @returns {Promise<Object>} - Auto-send settings or default settings
 */
export async function getTelegramAutoSendSettings() {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
        }
        
        const result = await chrome.storage.sync.get([AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND]);
        return result[AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND] || { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
        
    } catch (error) {
        console.error('Error getting Telegram auto-send settings:', error);
        return { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
    }
}

/**
 * Update Telegram auto-send failure count
 * @param {number} failureCount - New failure count
 * @returns {Promise<boolean>} - Success status
 */
export async function updateTelegramAutoSendFailureCount(failureCount) {
    try {
        const currentSettings = await getTelegramAutoSendSettings();
        const newSettings = {
            ...currentSettings,
            failureCount: failureCount,
            lastFailure: failureCount > 0 ? new Date().toISOString() : null,
            updatedAt: new Date().toISOString()
        };
        
        // Auto-disable if too many failures
        if (failureCount >= AUTO_SEND_CONFIG.MAX_FAILURE_COUNT) {
            newSettings.enabled = false;
            newSettings.autoDisabledAt = new Date().toISOString();
        }
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND]: newSettings
        });
        
        return true;
        
    } catch (error) {
        console.error('Error updating Telegram auto-send failure count:', error);
        return false;
    }
}

/**
 * Update Telegram auto-send success status
 * @returns {Promise<boolean>} - Success status
 */
export async function updateTelegramAutoSendSuccess() {
    try {
        const currentSettings = await getTelegramAutoSendSettings();
        const newSettings = {
            ...currentSettings,
            failureCount: 0,
            lastSent: new Date().toISOString(),
            lastFailure: null,
            updatedAt: new Date().toISOString()
        };
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.TELEGRAM_AUTO_SEND]: newSettings
        });
        
        return true;
        
    } catch (error) {
        console.error('Error updating Telegram auto-send success:', error);
        return false;
    }
} 