<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="16" height="16" rx="3" ry="3" fill="url(#gradient16)"/>
  
  <!-- Rocket -->
  <g transform="translate(8,8)">
    <!-- Rocket body -->
    <path d="M-1.5,-6 L1.5,-6 L2,-4 L2,2 L1,3 L-1,3 L-2,2 L-2,-4 Z" fill="white" opacity="0.9"/>
    <!-- Rocket tip -->
    <path d="M-1.5,-6 L0,-8 L1.5,-6 Z" fill="white"/>
    <!-- Flames -->
    <path d="M-1.5,3 L-0.5,5 L0,4 L0.5,5 L1.5,3 Z" fill="#ff6b6b" opacity="0.8"/>
    <!-- Window -->
    <circle cx="0" cy="-2" r="0.8" fill="#667eea" opacity="0.7"/>
  </g>
</svg> 