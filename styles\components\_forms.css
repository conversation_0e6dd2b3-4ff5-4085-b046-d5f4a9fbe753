/* Input Styles */
.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
}

input[type="password"], input[type="text"], textarea {
    flex: 1;
    padding: 14px 16px;
    border: 2px solid #2C2738;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #1D1A2A;
    color: #F9F9F9;
    font-family: inherit;
}

input[type="password"]:focus, input[type="text"]:focus, textarea:focus {
    outline: none;
    border-color: #5BA9F9;
    background: #1D1A2A;
    box-shadow: 0 0 0 3px rgba(91, 169, 249, 0.15);
}

input[type="password"]::placeholder, input[type="text"]::placeholder, textarea::placeholder {
    color: #B2AFC5;
}

textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    width: 100%;
}

/* Form Group Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #F9F9F9;
    font-size: 14px;
}

/* Radio Button Styles */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 12px 16px;
    border: 2px solid #2C2738;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: #1D1A2A;
    color: #B2AFC5;
}

.radio-label:hover {
    border-color: #5BA9F9;
    color: #F9F9F9;
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: transparent;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
}

.radio-label input[type="radio"]:checked {
    color: #F9F9F9;
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: #5BA9F9;
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #F9F9F9;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: transparent;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-custom::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 6px;
    height: 10px;
    border: solid #F9F9F9;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: #5BA9F9;
    border-color: #5BA9F9;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    opacity: 1;
}

.checkbox-label:hover .checkbox-custom {
    border-color: #5BA9F9;
}

/* Form Hint */
.form-hint {
    font-size: 12px;
    color: #B2AFC5;
    margin-top: 4px;
    font-style: italic;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-start;
    margin-top: 20px;
}

.form-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Form Actions - Alternative Layout */
.form-actions.centered {
    justify-content: center;
}

.form-actions.right-aligned {
    justify-content: flex-end;
}

/* Mobile Responsive */
@media (max-width: 540px) {
    .form-actions {
        flex-direction: column;
        gap: 12px;
    }
    
    .form-actions .btn {
        width: 100%;
        min-width: auto;
    }
}
