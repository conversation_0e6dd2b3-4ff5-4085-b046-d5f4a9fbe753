.error-notification, .success-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from { transform: translateX(-50%) translateY(-100%); }
    to { transform: translateX(-50%) translateY(0); }
}

.error-notification > div {
    background: linear-gradient(135deg, #FF5C5C, #FF8A80) !important;
    border: 1px solid rgba(255, 92, 92, 0.3);
    box-shadow: 0 8px 25px rgba(255, 92, 92, 0.3);
}

.success-notification > div {
    background: linear-gradient(135deg, #28a745, #34ce57) !important;
    border: 1px solid rgba(40, 167, 69, 0.3);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 8px;
    color: #dc3545;
    font-size: 14px;
}

.warning-icon {
    font-size: 16px;
    color: #ffc107;
}

.info-icon {
    font-size: 14px;
    color: #17a2b8;
}

.expiration-warning .warning-icon {
    font-size: 18px;
    flex-shrink: 0;
}
