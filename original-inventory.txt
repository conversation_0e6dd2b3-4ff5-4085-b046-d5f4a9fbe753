📋 Method Inventory Report
==================================================

📊 File: popup-original.js
  • Lines of code: 3302
  • Methods: 269
  • Classes: 1
  • Variables: 274
  • Imports: 9
  • Exports: 0

🎯 Methods by <PERSON>ope:
-------------------------
📁 AgentHustleAnalyzer: 53 methods
   • constructor()
   • async init()
   • async loadApiKey()
   • async saveApiKey(apiKey)
   • updateUI()
   ... and 48 more

📁 currentAnalysis: 12 methods
   • if(typeof result === 'string')
   • formatAnalysisContent(content, summary = false)
   • if(typeof content === 'string')
   • if(content.content)
   • if(content.tool_calls)
   ... and 7 more

📁 otherData: 4 methods
   • async copyResults()
   • if(!this.currentAnalysis || !this.currentAnalysis.result)
   • exportResults()
   • if(!this.currentAnalysis)

📁 exportData: 3 methods
   • async handleAutoSend(analysisType, result)
   • if(!proStatus.isPro)
   • if(!telegramAutoSend.enabled && !discordAutoSend.enabled)

📁 analysisData: 117 methods
   • if(telegramAutoSend.enabled)
   • if(discordAutoSend.enabled)
   • if(autoSendPromises.length > 0)
   • async handleTelegramAutoSend(analysisData)
   • if(!settings || !settings.botToken || !settings.chatId)
   ... and 112 more

📁 newEntry: 62 methods
   • async addDummyHistory()
   • async viewAnalysisFromHistory(analysisId)
   • if(analysisItem)
   • async deleteAnalysis(analysisId)
   • if(currentPageData.data.length === 0 && this.historyPagination.currentPage > 1)
   ... and 57 more

📁 promptData: 18 methods
   • if(this.currentEditingPrompt)
   • async deletePromptConfirm(promptId)
   • async deletePrompt(promptId)
   • if(!proStatus.isPro)
   • if(this.currentFilterTag)
   ... and 13 more

🏗️  Classes:
---------------
📦 AgentHustleAnalyzer (229 methods)
   • constructor()
   • updateUI()
   • if(hasApiKey)
   ... and 226 more methods

🔌 Public API Surface:
-------------------------
🔓 Public Methods:
   • constructor()
   • init()
   • loadApiKey()
   • saveApiKey(apiKey)
   • updateUI()
   • if(hasApiKey)
   • updateApiStatus()
   • if(this.apiKey)
   • showSection(sectionId)
   • if(el)
   ... and 259 more

⚠️  Potential Issues:
--------------------
🟡 DUPLICATE_METHODS: Duplicate method names found: if

🎯 Refactoring Guidance:
-------------------------
🚨 High method count - consider splitting into modules
📏 Large file - priority candidate for refactoring