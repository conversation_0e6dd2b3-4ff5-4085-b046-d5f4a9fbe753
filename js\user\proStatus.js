// Pro status management system
import { validate<PERSON>ro<PERSON><PERSON>, initializeCacheWarming, stopCacheWarming, initializePersistentCacheWarming } from '../auth/proValidator.js';
import { MEMBERSHIP_CONFIG } from '../../config.js';

/**
 * Check if current user has pro status
 * @returns {Promise<Object>} - Pro status information
 */
export async function checkProStatus() {
    try {
        // Get stored pro key
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (!proKey) {
            return {
                isPro: false,
                hasKey: false,
                message: 'No pro key configured'
            };
        }
        
        // Validate the key
        const validation = await validateProKey(proKey);
        
        // Check for expiration and handle warnings
        const membershipStatus = await processMembershipStatus(validation);
        
        // Store current status
        await chrome.storage.sync.set({
            hustleProStatus: {
                isPro: membershipStatus.isPro,
                lastChecked: new Date().toISOString(),
                cached: validation.cached,
                membershipDetails: membershipStatus.membershipDetails
            }
        });
        
        return membershipStatus;
        
    } catch (error) {
        console.error('Error checking pro status:', error);
        
        // Fallback to cached status
        try {
            const cachedResult = await chrome.storage.sync.get(['hustleProStatus']);
            const cached = cachedResult.hustleProStatus;
            
            if (cached) {
                return {
                    isPro: cached.isPro || false,
                    hasKey: true,
                    message: 'Using cached status due to error',
                    cached: true,
                    lastValidated: cached.lastChecked,
                    membershipDetails: cached.membershipDetails || null
                };
            }
        } catch (cacheError) {
            console.error('Cache fallback failed:', cacheError);
        }
        
        return {
            isPro: false,
            hasKey: false,
            message: 'Error checking pro status, defaulting to regular'
        };
    }
}

/**
 * Get detailed pro status with membership information
 * @param {string} proKey - Optional pro key to check (uses stored key if not provided)
 * @returns {Promise<Object>} - Detailed membership information
 */
export async function getDetailedProStatus(proKey = null) {
    try {
        if (!proKey) {
            const result = await chrome.storage.sync.get(['hustleProKey']);
            proKey = result.hustleProKey;
        }
        
        if (!proKey) {
            return {
                isPro: false,
                hasKey: false,
                message: 'No pro key configured',
                membershipDetails: null
            };
        }
        
        const validation = await validateProKey(proKey);
        return await processMembershipStatus(validation);
        
    } catch (error) {
        console.error('Error getting detailed pro status:', error);
        return {
            isPro: false,
            hasKey: true,
            message: 'Error retrieving membership details',
            membershipDetails: null
        };
    }
}

/**
 * Process membership status and handle expiration logic
 * @param {Object} validation - Validation result from validateProKey
 * @returns {Promise<Object>} - Processed membership status
 */
async function processMembershipStatus(validation) {
    const baseStatus = {
        isPro: validation.isPro,
        hasKey: true,
        message: validation.message,
        cached: validation.cached,
        lastValidated: validation.lastValidated,
        membershipDetails: validation.membershipDetails || null,
        enhanced: validation.enhanced || false,
        legacy: validation.legacy || false
    };
    
    // If no enhanced membership details, return basic status
    if (!validation.membershipDetails) {
        return baseStatus;
    }
    
    const details = validation.membershipDetails;
    
    // Check for expiration warnings
    if (details.daysRemaining <= 7 && details.daysRemaining > 0 && !details.isExpired) {
        const warningMessage = getExpirationWarningMessage(details.daysRemaining);
        baseStatus.expirationWarning = {
            daysRemaining: details.daysRemaining,
            message: warningMessage,
            urgent: details.daysRemaining <= 1
        };
    }
    
    // Handle expired memberships
    if (details.isExpired) {
        baseStatus.isPro = false;
        baseStatus.message = 'Pro membership has expired';
        baseStatus.expired = true;
    }
    
    return baseStatus;
}

/**
 * Get expiration warning message based on days remaining
 * @param {number} daysRemaining - Days until expiration
 * @returns {string} - Warning message
 */
function getExpirationWarningMessage(daysRemaining) {
    if (daysRemaining <= 1) {
        return '🚨 Pro membership expires tomorrow!';
    } else if (daysRemaining <= 3) {
        return `⚠️ Pro membership expires in ${daysRemaining} days`;
    } else if (daysRemaining <= 7) {
        return `⚠️ Pro membership expires in ${daysRemaining} days`;
    }
    return '';
}

/**
 * Check membership expiration and handle automatic transitions
 * @param {string} proKey - Optional pro key to check
 * @returns {Promise<Object>} - Expiration check result
 */
export async function checkMembershipExpiration(proKey = null) {
    try {
        const status = await getDetailedProStatus(proKey);
        
        if (!status.membershipDetails) {
            return {
                checked: true,
                expired: false,
                message: 'Legacy membership format - no expiration tracking'
            };
        }
        
        const details = status.membershipDetails;
        
        return {
            checked: true,
            expired: details.isExpired,
            daysRemaining: details.daysRemaining,
            expiresAt: details.expiresAt,
            message: details.isExpired ? 'Membership has expired' : `${details.daysRemaining} days remaining`,
            actionTaken: details.isExpired ? 'Transitioned to regular user' : 'No action needed'
        };
        
    } catch (error) {
        console.error('Error checking membership expiration:', error);
        return {
            checked: false,
            expired: false,
            message: 'Failed to check expiration status'
        };
    }
}

/**
 * Get membership time remaining details
 * @param {string} proKey - Optional pro key to check
 * @returns {Promise<Object>} - Time remaining details
 */
export async function getMembershipTimeRemaining(proKey = null) {
    try {
        const status = await getDetailedProStatus(proKey);
        
        if (!status.membershipDetails) {
            return {
                hasExpiration: false,
                message: 'Legacy membership - no expiration tracking'
            };
        }
        
        const details = status.membershipDetails;
        const expiresAt = new Date(details.expiresAt);
        const now = new Date();
        const timeRemaining = expiresAt - now;
        
        if (timeRemaining <= 0) {
            return {
                hasExpiration: true,
                expired: true,
                daysRemaining: 0,
                hoursRemaining: 0,
                message: 'Membership has expired'
            };
        }
        
        const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));
        const hoursRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60));
        
        return {
            hasExpiration: true,
            expired: false,
            daysRemaining,
            hoursRemaining,
            expiresAt: details.expiresAt,
            message: daysRemaining > 1 ? `${daysRemaining} days remaining` : `${hoursRemaining} hours remaining`
        };
        
    } catch (error) {
        console.error('Error getting membership time remaining:', error);
        return {
            hasExpiration: false,
            message: 'Failed to calculate time remaining'
        };
    }
}

/**
 * Update last used timestamp and usage count
 * @param {string} proKey - Optional pro key to update
 * @returns {Promise<boolean>} - Success status
 */
export async function updateLastUsed(proKey = null) {
    try {
        if (!proKey) {
            const result = await chrome.storage.sync.get(['hustleProKey']);
            proKey = result.hustleProKey;
        }
        
        if (!proKey) {
            return false;
        }
        
        // This will be handled automatically by the validation process
        // when the key is validated, usage is updated
        await validateProKey(proKey);
        return true;
        
    } catch (error) {
        console.error('Error updating last used:', error);
        return false;
    }
}

/**
 * Set user's pro key
 * @param {string} proKey - The user's pro key
 * @returns {Promise<Object>} - Validation result
 */
export async function setProKey(proKey) {
    try {
        if (!proKey || proKey.trim().length === 0) {
            throw new Error('Pro key cannot be empty');
        }
        
        // Validate the key first
        const validation = await validateProKey(proKey.trim());
        
        if (validation.isPro) {
            // Store the key if valid
            await chrome.storage.sync.set({
                hustleProKey: proKey.trim(),
                hustleProStatus: {
                    isPro: true,
                    lastChecked: new Date().toISOString(),
                    cached: validation.cached
                }
            });
            
            // Initialize cache warming for the new key
            await initializeCacheWarming();
            
            // PHASE 2: Initialize persistent cache warming
            await initializePersistentCacheWarming();
            
            return {
                success: true,
                isPro: true,
                message: 'Pro key validated and saved successfully!'
            };
        } else {
            return {
                success: false,
                isPro: false,
                message: 'Invalid pro key. Please check your key and try again.'
            };
        }
        
    } catch (error) {
        console.error('Error setting pro key:', error);
        return {
            success: false,
            isPro: false,
            message: `Failed to validate pro key: ${error.message}`
        };
    }
}

/**
 * Remove pro key and reset to regular user
 * @returns {Promise<boolean>} - Success status
 */
export async function removeProKey() {
    try {
        // Stop cache warming first
        stopCacheWarming();
        
        await chrome.storage.sync.remove(['hustleProKey', 'hustleProStatus']);
        return true;
    } catch (error) {
        console.error('Error removing pro key:', error);
        return false;
    }
}

/**
 * Get current pro key (for display purposes only - masked)
 * @returns {Promise<string>} - Masked pro key or empty string
 */
export async function getMaskedProKey() {
    try {
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (!proKey) {
            return '';
        }
        
        // Mask the key for display (show first 4 and last 4 characters)
        if (proKey.length <= 8) {
            return '*'.repeat(proKey.length);
        }
        
        return proKey.substring(0, 4) + '*'.repeat(proKey.length - 8) + proKey.substring(proKey.length - 4);
        
    } catch (error) {
        console.error('Error getting masked pro key:', error);
        return '';
    }
}

/**
 * Force refresh pro status (re-validate against remote)
 * @returns {Promise<Object>} - Updated pro status
 */
export async function refreshProStatus() {
    try {
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (!proKey) {
            // Stop cache warming if no key
            stopCacheWarming();
            return {
                isPro: false,
                hasKey: false,
                message: 'No pro key configured'
            };
        }
        
        // Clear cached status to force fresh validation
        await chrome.storage.sync.remove(['hustleProStatus']);
        
        // Force validation to refresh status
        const validation = await validateProKey(proKey, true);
        
        // Initialize cache warming for active pro users
        if (validation.isPro) {
            await initializeCacheWarming();
            await initializePersistentCacheWarming();
        } else {
            stopCacheWarming();
        }
        
        // Re-check status
        return await checkProStatus();
        
    } catch (error) {
        console.error('Error refreshing pro status:', error);
        return {
            isPro: false,
            hasKey: false,
            message: 'Failed to refresh pro status'
        };
    }
} 