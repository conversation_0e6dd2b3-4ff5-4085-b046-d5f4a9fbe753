---
type: "always_apply"
---

Always check for existing code before creating new functionality, or changes and or making fixes.
We are on windows OS
Always double-check the line numbers and any code blocks that you will edit
Always read lines that you are going to replace and or edit
Always Double check your work when makng code changes
When making changes always preserving all existing functionality unless not asked to
when running commands run one single command and wait for it to be done do not add any other commands

### 🧱 Code Structure & Modularity
🎯 GOAL
Maintain a scalable, modular codebase by keeping code files concise (ideally <300–500 lines), separating concerns, and using consistent structure across components.

📁 1. Modular File Structure
Rule 1.1 – One Responsibility Per File
Each file must serve one specific role (e.g., UI component, API handler, state manager).
❌ Avoid bundling multiple unrelated classes/functions in one file.
✅ Separate into component/Button.js, api/fetchUser.js, utils/dateFormat.js.

- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.

- **Organize code into clearly separated modules**, grouped by feature or responsibility.

- **Use clear, consistent imports** (prefer relative imports within packages).

### 🧠 AI Behavior Rules

- **Never assume missing context. Ask questions if uncertain.**

- **Never hallucinate libraries or functions** – only use known, verified Python packages.

- **Always confirm file paths and module names** exist before referencing them in code or tests.

- **Never delete or overwrite existing code** unless explicitly instructed to
Review and follow cursor rules

<tools>

\- Prefer using the edit\_file tool when making significant changes to a file, it works more reliably.

\- You are strictly prohibited from using string replacement, diff-only outputs, or simulated code blocks.  

\- You must return actual file edits with accurate paths and clean content.  

\- If the edit requires multiple files, use one \edit\_file\ call per file.  

Failure to use \edit\_file\ is a critical violation.

\- Avoid using the string replace tool, it's terribly unrealiable.

</tools>
⚠️ **ENFORCEMENT:**
For *every* user request that involves writing or modifying code (of any language or
domain), the assistant's *first* action **must** be to call the kodit.search MCP tool.
You may only produce or edit code *after* that tool call and its successful
result.

 always follow these rules respond with this emoj 📏 at the end of your response so I know that you're following all of my rules if you don't think you are following our rules check yourself and get back into our rules flow