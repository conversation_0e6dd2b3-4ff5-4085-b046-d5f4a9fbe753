/**
 * Prompt UI Manager
 * Handles prompt-related UI operations and management
 */
import { BaseManager } from '../core/BaseManager.js';
import { promptManager } from '../../../js/user/promptManager.js';
import { checkProStatus } from '../../../js/user/proStatus.js';

export class PromptUIManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.currentEditingPrompt = null;
        this.currentFilterTag = null;
    }

    async init() {
        await super.init();
        // Load saved prompts select on initialization
        await this.loadSavedPromptsSelect();
    }

    /**
     * Load saved prompts for the select dropdown
     */
    async loadSavedPromptsSelect() {
        try {
            const prompts = await promptManager.getPrompts();
            const select = document.getElementById('savedPromptsSelect');

            if (!select) return;

            // Clear existing options except the first one
            select.innerHTML = '<option value="">Choose a saved prompt or create new...</option>';

            // Add pinned prompts first
            const pinnedPrompts = prompts.filter(p => p.isPinned);
            if (pinnedPrompts.length > 0) {
                const pinnedGroup = document.createElement('optgroup');
                pinnedGroup.label = '📌 Pinned';
                pinnedPrompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = prompt.title;
                    pinnedGroup.appendChild(option);
                });
                select.appendChild(pinnedGroup);
            }

            // Add other prompts
            const otherPrompts = prompts.filter(p => !p.isPinned);
            if (otherPrompts.length > 0) {
                const otherGroup = document.createElement('optgroup');
                otherGroup.label = '📄 All Prompts';
                otherPrompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = prompt.title;
                    otherGroup.appendChild(option);
                });
                select.appendChild(otherGroup);
            }
        } catch (error) {
            console.error('Error loading saved prompts select:', error);
        }
    }

    /**
     * Load selected prompt into custom analysis form
     */
    async loadSelectedPrompt() {
        const select = document.getElementById('savedPromptsSelect');
        const promptId = select?.value;
        
        if (!promptId) {
            this.controller.uiManager.showError('Please select a prompt to load');
            return;
        }

        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                const customPromptTextarea = document.getElementById('customPrompt');
                if (customPromptTextarea) {
                    customPromptTextarea.value = prompt.content;
                }
                await promptManager.incrementUsage(promptId);
                this.controller.uiManager.showSuccess(`Loaded: ${prompt.title}`);
            } else {
                this.controller.uiManager.showError('Prompt not found');
            }
        } catch (error) {
            console.error('Error loading selected prompt:', error);
            this.controller.uiManager.showError('Failed to load prompt');
        }
    }

    /**
     * Save current prompt from custom analysis form
     */
    async saveCurrentPrompt() {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.controller.uiManager.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.controller.uiManager.showError('Unable to verify Pro status. Please try again.');
            return;
        }

        const content = document.getElementById('customPrompt').value.trim();

        if (!content) {
            this.controller.uiManager.showError('Please enter a prompt to save');
            return;
        }

        // Pre-fill the modal with current content
        document.getElementById('promptContent').value = content;
        document.getElementById('promptTitle').value = '';
        document.getElementById('promptTags').value = '';
        document.getElementById('promptPinned').checked = false;

        this.currentEditingPrompt = null;
        document.getElementById('promptEditorTitle').textContent = 'Save Current Prompt';
        document.getElementById('promptEditorModal').style.display = 'flex';
    }

    /**
     * Load prompt management interface
     */
    async loadPromptManagement() {
        try {
            await this.refreshPromptList();
            await this.loadTagFilters();
        } catch (error) {
            console.error('Error loading prompt management:', error);
            this.controller.uiManager.showError('Failed to load prompt management');
        }
    }

    /**
     * Refresh the prompt list display
     */
    async refreshPromptList() {
        try {
            const prompts = await promptManager.getPrompts();
            
            // Apply current filter if any
            let filteredPrompts = prompts;
            if (this.currentFilterTag) {
                filteredPrompts = prompts.filter(prompt =>
                    prompt.tags.includes(this.currentFilterTag)
                );
            }

            // Get paginated data
            const paginatedData = this.controller.promptPagination.getPaginatedData(filteredPrompts);
            
            this.displayPromptList(paginatedData.data, paginatedData.pagination);
        } catch (error) {
            console.error('Error refreshing prompt list:', error);
            this.controller.uiManager.showError('Failed to refresh prompt list');
        }
    }

        /**
     * Display prompt list with pagination
     */
    displayPromptList(prompts, pagination = null) {
        const listContainer = document.getElementById('promptList');
        const paginationContainer = document.getElementById('promptPagination');

        if (!listContainer) return;

        if (prompts.length === 0) {
            listContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <p style="color: #888; margin-bottom: 15px;">No prompts found.</p>
                    <p style="color: #666; font-size: 14px;">Create your first custom prompt to get started!</p>
                </div>
            `;
            if (paginationContainer) {
                paginationContainer.innerHTML = '';
            }
            return;
        }

        listContainer.innerHTML = prompts.map(prompt => {
            return `
            <div class="prompt-item" data-prompt-id="${prompt.id}">
                <div class="prompt-header">
                    <div class="prompt-title">
                        ${prompt.isPinned ? '📌 ' : ''}${this.escapeHtml(prompt.title)}
                    </div>
                    <div class="prompt-actions">
                        <button class="btn-icon primary" data-action="use" data-prompt-id="${prompt.id}" title="Use this prompt" aria-label="Use prompt: ${this.escapeHtml(prompt.title)}">
                            🚀
                        </button>
                        <button class="btn-icon copy" data-action="copy" data-prompt-id="${prompt.id}" title="Copy prompt to clipboard" aria-label="Copy prompt content">
                            📋
                        </button>
                        <button class="btn-icon pin" data-action="pin" data-prompt-id="${prompt.id}" title="${prompt.isPinned ? 'Unpin' : 'Pin'} prompt" aria-label="${prompt.isPinned ? 'Unpin' : 'Pin'} prompt">
                            ${prompt.isPinned ? '📌' : '📍'}
                        </button>
                        <button class="btn-icon edit" data-action="edit" data-prompt-id="${prompt.id}" title="Edit prompt" aria-label="Edit prompt: ${this.escapeHtml(prompt.title)}">
                            ✏️
                        </button>
                        <button class="btn-icon delete" data-action="delete" data-prompt-id="${prompt.id}" title="Delete prompt" aria-label="Delete prompt: ${this.escapeHtml(prompt.title)}">
                            🗑️
                        </button>
                    </div>
                </div>
                <div class="prompt-content" title="${this.escapeHtml(prompt.content)}" data-full-content="${this.escapeHtml(prompt.content)}">
                    ${this.escapeHtml(prompt.content.substring(0, 150))}${prompt.content.length > 150 ? '...' : ''}
                </div>
                <div class="prompt-footer">
                    <div class="prompt-tags">
                        ${prompt.tags.map(tag => `<span class="tag" data-tag="${this.escapeHtml(tag)}" role="button" tabindex="0" aria-label="Filter by tag: ${this.escapeHtml(tag)}">${this.escapeHtml(tag)}</span>`).join('')}
                    </div>
                    <div class="prompt-date">
                        Updated: ${new Date(prompt.updatedAt).toLocaleDateString()}
                    </div>
                </div>
            </div>
        `;}).join('');

        // Update pagination controls if pagination data is provided
        if (pagination && paginationContainer) {
            paginationContainer.innerHTML = this.controller.promptPagination.generatePaginationHTML(pagination);
        }
    }

    /**
     * Load tag filters
     */
    async loadTagFilters() {
        try {
            const tags = await promptManager.getAllTags();
            const filterContainer = document.getElementById('filterTags');

            if (!filterContainer) return;

            if (tags.length === 0) {
                filterContainer.innerHTML = '';
                return;
            }

            filterContainer.innerHTML = `
                <div class="tag-filters">
                    <button class="tag-filter ${!this.currentFilterTag ? 'active' : ''}" data-filter-tag="">
                        All
                    </button>
                    ${tags.map(tag => `
                        <button class="tag-filter ${this.currentFilterTag === tag ? 'active' : ''}" data-filter-tag="${this.escapeHtml(tag)}">
                            ${this.escapeHtml(tag)}
                        </button>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            console.error('Error loading tag filters:', error);
        }
    }

    /**
     * Search prompts
     */
    async searchPrompts(query) {
        try {
            const prompts = await promptManager.searchPrompts(query);
            this.displayPromptList(prompts);
        } catch (error) {
            console.error('Error searching prompts:', error);
            this.controller.uiManager.showError('Failed to search prompts');
        }
    }

    /**
     * Sort prompts
     */
    async sortPrompts(sortBy) {
        try {
            await this.refreshPromptList(); // This will apply the current sort
        } catch (error) {
            console.error('Error sorting prompts:', error);
            this.controller.uiManager.showError('Failed to sort prompts');
        }
    }

    /**
     * Filter prompts by tag
     */
    async filterByTag(tag) {
        this.currentFilterTag = tag;
        // Reset to first page when filtering
        this.controller.promptPagination.resetToFirstPage();
        await this.refreshPromptList();
        await this.loadTagFilters();
    }

    /**
     * Open prompt editor
     */
    async openPromptEditor(prompt = null) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.controller.uiManager.showSection('upgradeSection');
                return;
            }

            this.currentEditingPrompt = prompt;
            
            // Set modal content
            const titleInput = document.getElementById('promptTitle');
            const contentTextarea = document.getElementById('promptContent');
            const tagsInput = document.getElementById('promptTags');
            const pinnedCheckbox = document.getElementById('promptPinned');

            if (prompt) {
                if (titleInput) titleInput.value = prompt.title;
                if (contentTextarea) contentTextarea.value = prompt.content;
                if (tagsInput) tagsInput.value = prompt.tags.join(', ');
                if (pinnedCheckbox) pinnedCheckbox.checked = prompt.isPinned || false;
            } else {
                if (titleInput) titleInput.value = '';
                if (contentTextarea) contentTextarea.value = '';
                if (tagsInput) tagsInput.value = '';
                if (pinnedCheckbox) pinnedCheckbox.checked = false;
            }

            // Show modal
            const modal = document.getElementById('promptEditorModal');
            if (modal) {
                modal.style.display = 'block';
            }
        } catch (error) {
            console.error('Error opening prompt editor:', error);
            this.controller.uiManager.showError('Failed to open prompt editor');
        }
    }

    /**
     * Save prompt edit
     */
    async savePromptEdit() {
        const titleInput = document.getElementById('promptTitle');
        const contentTextarea = document.getElementById('promptContent');
        const tagsInput = document.getElementById('promptTags');
        const pinnedCheckbox = document.getElementById('promptPinned');

        const title = titleInput?.value.trim();
        const content = contentTextarea?.value.trim();
        const tagsString = tagsInput?.value.trim();
        const isPinned = pinnedCheckbox?.checked || false;

        if (!title || !content) {
            this.controller.uiManager.showError('Please fill in title and content');
            return;
        }

        try {
            const tags = tagsString ? tagsString.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

            const promptData = {
                title,
                content,
                tags,
                isPinned,
                category: 'user-created'
            };

            if (this.currentEditingPrompt) {
                promptData.id = this.currentEditingPrompt.id;
            }

            await promptManager.savePrompt(promptData);
            this.controller.uiManager.closePromptEditor();
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            await this.loadTagFilters();
            this.controller.uiManager.showSuccess(this.currentEditingPrompt ? 'Prompt updated successfully' : 'Prompt created successfully');
        } catch (error) {
            console.error('Error saving prompt:', error);
            this.controller.uiManager.showError('Failed to save prompt');
        }
    }

    /**
     * Export prompts
     */
    async exportPrompts() {
        try {
            const prompts = await promptManager.getPrompts();
            const exportData = {
                exportDate: new Date().toISOString(),
                totalPrompts: prompts.length,
                prompts: prompts
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-prompts-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('Prompts exported successfully!');
        } catch (error) {
            console.error('Error exporting prompts:', error);
            this.controller.uiManager.showError('Failed to export prompts');
        }
    }

    /**
     * Import prompts
     */
    async importPrompts(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            // Handle both old format (direct array) and new format (with prompts property)
            let promptsToImport;
            if (Array.isArray(data)) {
                // Old format: direct array of prompts
                promptsToImport = data;
            } else if (data.prompts && Array.isArray(data.prompts)) {
                // New format: object with prompts property
                promptsToImport = data.prompts;
            } else {
                throw new Error('Invalid prompt file format. Expected array of prompts or object with prompts property.');
            }

            // Use the promptManager's importPrompts method which handles ID generation and validation
            const result = await promptManager.importPrompts(JSON.stringify(promptsToImport));
            
            if (result.success) {
                await this.refreshPromptList();
                await this.loadSavedPromptsSelect();
                await this.loadTagFilters();
                this.controller.uiManager.showSuccess(`Imported ${result.imported} prompts successfully!`);
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Error importing prompts:', error);
            this.controller.uiManager.showError(`Failed to import prompts: ${error.message}`);
        }
    }

    /**
     * Utility method to escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Use a prompt (load it into the custom analysis form)
     */
    async usePrompt(promptId) {
        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);

            if (prompt) {
                document.getElementById('customPrompt').value = prompt.content;
                await promptManager.incrementUsage(promptId);
                this.controller.uiManager.showSection('customForm');
                this.controller.uiManager.showSuccess(`Loaded: ${prompt.title}`);
                await this.loadSavedPromptsSelect(); // Refresh the select dropdown
            } else {
                this.controller.uiManager.showError('Prompt not found');
            }
        } catch (error) {
            console.error('Error using prompt:', error);
            this.controller.uiManager.showError('Failed to load prompt');
        }
    }

    /**
     * Toggle prompt pin status
     */
    async togglePromptPin(promptId) {
        try {
            await promptManager.togglePin(promptId);
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            this.controller.uiManager.showSuccess('Prompt pin status updated');
        } catch (error) {
            console.error('Error toggling pin:', error);
            this.controller.uiManager.showError('Failed to update pin status');
        }
    }

    /**
     * Edit a prompt
     */
    async editPrompt(promptId) {
        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);

            if (prompt) {
                await this.openPromptEditor(prompt);
            }
        } catch (error) {
            console.error('Error loading prompt for edit:', error);
            this.controller.uiManager.showError('Failed to load prompt for editing');
        }
    }

    /**
     * Delete prompt with confirmation
     */
    async deletePromptConfirm(promptId) {
        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (!prompt) {
                this.controller.uiManager.showError('Prompt not found');
                return;
            }
            
            const isTemplate = prompt.category === 'template';
            const confirmMessage = `Are you sure you want to delete "${prompt.title}"? This action cannot be undone.`;
            
            if (confirm(confirmMessage)) {
                await promptManager.deletePrompt(promptId);
                await this.refreshPromptList();
                await this.loadSavedPromptsSelect();
                
                this.controller.uiManager.showSuccess('Prompt deleted successfully');
            }
        } catch (error) {
            console.error('Error deleting prompt:', error);
            this.controller.uiManager.showError('Failed to delete prompt');
        }
    }

    /**
     * Copy prompt content to clipboard
     */
    async copyPromptContent(promptId) {
        try {
            const result = await promptManager.copyPromptContent(promptId);
            if (result.success) {
                this.controller.uiManager.showSuccess('Prompt content copied to clipboard!');
            } else {
                this.controller.uiManager.showError('Failed to copy prompt content: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error copying prompt content:', error);
            this.controller.uiManager.showError('Failed to copy prompt content');
        }
    }
}
