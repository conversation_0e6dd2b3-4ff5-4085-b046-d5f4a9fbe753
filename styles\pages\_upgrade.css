.upgrade-content {
    text-align: center;
}

.upgrade-hero {
    margin-bottom: 30px;
}

.upgrade-icon {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.upgrade-hero h2 {
    font-size: 24px;
    font-weight: 700;
    color: #F9F9F9;
    margin-bottom: 8px;
}

.upgrade-hero p {
    font-size: 16px;
    color: #B2AFC5;
    margin-bottom: 0;
}

.upgrade-features {
    margin-bottom: 30px;
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-2px);
}

.pro-key-form {
    margin-bottom: 24px;
}

.pro-key-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #F9F9F9;
    font-size: 16px;
    transition: all 0.3s ease;
}

.pro-key-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(168, 192, 255, 0.1);
}

.pro-key-input::placeholder {
    color: #B2AFC5;
}

.key-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 14px;
}

.key-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6B7280;
    flex-shrink: 0;
}

.key-status.validating .status-indicator {
    background: #FFD84D;
    animation: blink 1s infinite;
}

.key-status.valid .status-indicator {
    background: #10B981;
}

.key-status.invalid .status-indicator {
    background: #EF4444;
}

.key-status .status-text {
    color: #B2AFC5;
}

.key-status.valid .status-text {
    color: #10B981;
}

.key-status.invalid .status-text {
    color: #EF4444;
}

.upgrade-footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.upgrade-footer p {
    font-size: 14px;
    color: #B2AFC5;
    margin: 0;
}

.upgrade-footer a {
    color: #5BA9F9;
    text-decoration: none;
    font-weight: 500;
}

.upgrade-footer a:hover {
    text-decoration: underline;
}

.upgrade-stats {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(168, 192, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: #A8C0FF;
    border: 1px solid rgba(168, 192, 255, 0.2);
    white-space: nowrap;
}

.feature-item.featured {
    background: linear-gradient(135deg, rgba(168, 192, 255, 0.1), rgba(255, 216, 77, 0.05));
    border: 1px solid rgba(168, 192, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.feature-item.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #A8C0FF, #FFD84D);
}

.social-proof {
    margin-bottom: 16px;
    padding: 16px;
    background: rgba(168, 192, 255, 0.05);
    border-radius: 12px;
    border-left: 3px solid #A8C0FF;
}

.testimonial {
    font-style: italic;
    color: #B2AFC5;
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.cta-section {
    text-align: center;
}

.cta-text {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
}

.feature-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    color: #A8C0FF;
}

.feature-text {
    flex: 1;
}

.feature-text strong {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 4px;
}

.feature-text p {
    margin: 0;
    font-size: 14px;
    color: #B2AFC5;
    line-height: 1.4;
}

.feature-highlight {
    background: linear-gradient(135deg, rgba(91, 169, 249, 0.15), rgba(168, 192, 255, 0.1));
    border: 1px solid rgba(91, 169, 249, 0.3);
    padding: 20px;
    border-radius: 16px;
    margin: 20px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.pro-upgrade-notice .upgrade-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.key-status-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    overflow: hidden;
    min-width: 0;
}

.key-change-form .key-status {
    margin-bottom: 16px;
}

    .key-status-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
