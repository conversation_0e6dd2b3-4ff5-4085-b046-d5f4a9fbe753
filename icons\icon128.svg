<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient128" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6b73e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="rocketGradient128" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f8f8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="flameGradient128" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff5252;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ff4757;stop-opacity:0.7" />
    </radialGradient>
    <radialGradient id="windowGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#7c8aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:0.8" />
    </radialGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0,0,0,0.15)"/>
    </filter>
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
    </filter>
  </defs>
  
  <!-- Background with shadow -->
  <rect width="128" height="128" rx="20" ry="20" fill="url(#gradient128)" filter="url(#shadow)"/>
  
  <!-- Rocket -->
  <g transform="translate(64,64)">
    <!-- Rocket body -->
    <path d="M-12,-48 L12,-48 L16,-32 L16,16 L8,24 L-8,24 L-16,16 L-16,-32 Z" 
          fill="url(#rocketGradient128)" 
          stroke="rgba(255,255,255,0.5)" 
          stroke-width="1"
          filter="url(#innerShadow)"/>
    
    <!-- Rocket tip -->
    <path d="M-12,-48 L0,-64 L12,-48 Z" 
          fill="white" 
          stroke="rgba(255,255,255,0.3)" 
          stroke-width="0.5"
          filter="url(#innerShadow)"/>
    
    <!-- Side fins -->
    <path d="M-16,-16 L-24,-8 L-24,8 L-16,8 Z" 
          fill="rgba(255,255,255,0.95)" 
          stroke="rgba(255,255,255,0.4)" 
          stroke-width="0.5"/>
    <path d="M16,-16 L24,-8 L24,8 L16,8 Z" 
          fill="rgba(255,255,255,0.95)" 
          stroke="rgba(255,255,255,0.4)" 
          stroke-width="0.5"/>
    
    <!-- Flames -->
    <path d="M-12,24 L-4,40 L0,32 L4,40 L12,24 Z" fill="url(#flameGradient128)"/>
    <path d="M-8,24 L-2,36 L0,30 L2,36 L8,24 Z" fill="#ff4757" opacity="0.9"/>
    <path d="M-4,24 L-1,32 L0,28 L1,32 L4,24 Z" fill="#ff3742" opacity="0.7"/>
    <path d="M-2,24 L-0.5,30 L0,26 L0.5,30 L2,24 Z" fill="#ff2d3a" opacity="0.5"/>
    
    <!-- Main windows -->
    <circle cx="0" cy="-16" r="6" fill="url(#windowGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="0.5"/>
    <circle cx="0" cy="0" r="4" fill="url(#windowGradient)" opacity="0.8" stroke="rgba(255,255,255,0.3)" stroke-width="0.3"/>
    <circle cx="0" cy="10" r="2.5" fill="url(#windowGradient)" opacity="0.6"/>
    
    <!-- Window reflections -->
    <ellipse cx="-2" cy="-18" rx="2" ry="4" fill="rgba(255,255,255,0.5)"/>
    <ellipse cx="-1.5" cy="-1.5" rx="1.5" ry="3" fill="rgba(255,255,255,0.4)"/>
    <ellipse cx="-1" cy="9" rx="1" ry="2" fill="rgba(255,255,255,0.3)"/>
    
    <!-- Body details and panels -->
    <rect x="-8" y="-32" width="16" height="4" fill="rgba(102,126,234,0.7)" rx="2"/>
    <rect x="-8" y="-24" width="16" height="2" fill="rgba(102,126,234,0.5)" rx="1"/>
    <rect x="-6" y="-40" width="12" height="1.5" fill="rgba(102,126,234,0.4)" rx="0.75"/>
    <rect x="-4" y="-44" width="8" height="1" fill="rgba(102,126,234,0.3)" rx="0.5"/>
    
    <!-- Side panels -->
    <rect x="-12" y="-20" width="2" height="24" fill="rgba(102,126,234,0.3)" rx="1"/>
    <rect x="10" y="-20" width="2" height="24" fill="rgba(102,126,234,0.3)" rx="1"/>
    
    <!-- Rivets and details -->
    <circle cx="-6" cy="-8" r="0.8" fill="rgba(102,126,234,0.5)"/>
    <circle cx="6" cy="-8" r="0.8" fill="rgba(102,126,234,0.5)"/>
    <circle cx="-6" cy="8" r="0.8" fill="rgba(102,126,234,0.5)"/>
    <circle cx="6" cy="8" r="0.8" fill="rgba(102,126,234,0.5)"/>
    
    <!-- Additional details -->
    <rect x="-10" y="-12" width="20" height="0.5" fill="rgba(102,126,234,0.3)" rx="0.25"/>
    <rect x="-10" y="-4" width="20" height="0.5" fill="rgba(102,126,234,0.3)" rx="0.25"/>
    <rect x="-10" y="4" width="20" height="0.5" fill="rgba(102,126,234,0.3)" rx="0.25"/>
    
    <!-- Fin details -->
    <rect x="-20" y="-12" width="4" height="1" fill="rgba(102,126,234,0.4)" rx="0.5"/>
    <rect x="16" y="-12" width="4" height="1" fill="rgba(102,126,234,0.4)" rx="0.5"/>
    
    <!-- Exhaust nozzles -->
    <ellipse cx="-6" cy="24" rx="1.5" ry="3" fill="rgba(255,255,255,0.8)"/>
    <ellipse cx="6" cy="24" rx="1.5" ry="3" fill="rgba(255,255,255,0.8)"/>
    <ellipse cx="0" cy="24" rx="2" ry="4" fill="rgba(255,255,255,0.9)"/>
  </g>
  
  <!-- Outer glow and border -->
  <rect width="128" height="128" rx="20" ry="20" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  
  <!-- Subtle inner highlight -->
  <rect x="2" y="2" width="124" height="124" rx="18" ry="18" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/>
</svg> 