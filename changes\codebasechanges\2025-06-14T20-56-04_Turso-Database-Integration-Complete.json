{"timestamp": "2025-06-14T20:56:04", "author": "AI Assistant", "task_reference": "Turso Database Integration Complete Migration", "session_summary": "Complete migration from Vercel serverless with hardcoded keys to Render deployment with Turso database integration. This represents a major architectural change from static validation to dynamic database-backed pro key management.", "migration_type": "Architecture Migration", "deployment_change": {"from": "Vercel Serverless Functions", "to": "Render Express.js Application", "database": "Turso (libSQL) with normalized schema"}, "changes": [{"file_path": "vercel-api/package.json", "change_type": "major_update", "git_diff": "--- a/vercel-api/package.json\n+++ b/vercel-api/package.json\n@@ -1,17 +1,29 @@\n {\n   \"name\": \"hustleplug-pro-validation\",\n   \"version\": \"1.0.0\",\n   \"description\": \"Enhanced Pro Key Validation API for HustlePlug Chrome Extension\",\n+  \"type\": \"module\",\n+  \"main\": \"app.js\",\n   \"scripts\": {\n+    \"start\": \"node app.js\",\n+    \"dev\": \"nodemon app.js\",\n     \"deploy\": \"vercel --prod\",\n     \"generate-hashes\": \"node generate-hashes.js\",\n+    \"setup-db\": \"turso db shell hustleplug-pro-keys < schema.sql\",\n+    \"migrate\": \"node migrate-to-turso.js\",\n     \"test-api\": \"node test-api.js\"\n   },\n-  \"keywords\": [\"chrome-extension\", \"pro-validation\", \"membership\"],\n+  \"keywords\": [\"chrome-extension\", \"pro-validation\", \"membership\", \"turso\"],\n   \"author\": \"HustlePlug\",\n   \"license\": \"MIT\",\n   \"dependencies\": {\n+    \"@libsql/client\": \"^0.5.2\",\n+    \"express\": \"^4.18.2\",\n+    \"cors\": \"^2.8.5\",\n+    \"helmet\": \"^7.1.0\",\n+    \"dotenv\": \"^16.3.1\"\n+  },\n+  \"devDependencies\": {\n+    \"nodemon\": \"^3.0.2\"\n   }\n }", "summary": "Added Express.js dependencies and Turso client, converted to ES modules, added database setup scripts", "rollback_note": "Safe to rollback - will revert to Vercel serverless architecture. Ensure environment variables are updated."}, {"file_path": "vercel-api/app.js", "change_type": "new_file", "git_diff": "--- /dev/null\n+++ b/vercel-api/app.js\n@@ -0,0 +1,163 @@\n+// HustlePlug Pro Validation API - Express.js Server\n+// Migrated from Vercel serverless to Express for Render deployment\n+// Maintains full backward compatibility with Chrome extension\n+\n+import express from 'express';\n+import cors from 'cors';\n+import helmet from 'helmet';\n+import dotenv from 'dotenv';\n+import { testConnection } from './db/connection.js';\n+import { checkDatabaseHealth } from './db/queries.js';\n+\n+// Load environment variables\n+dotenv.config();\n+\n+// Import the validation handler (converted from serverless function)\n+import validate<PERSON>eyHandler from './api/validate-key.js';\n+\n+const app = express();\n+const PORT = process.env.PORT || 3000;\n+\n+// Security middleware\n+app.use(helmet({\n+    crossOriginResourcePolicy: { policy: \"cross-origin\" }\n+}));\n+\n+// CORS configuration - maintain exact same headers as Vercel version\n+app.use(cors({\n+    origin: '*',\n+    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],\n+    allowedHeaders: ['Content-Type', 'Authorization'],\n+    credentials: false\n+}));\n+\n+// Body parsing middleware\n+app.use(express.json({ limit: '10mb' }));\n+app.use(express.urlencoded({ extended: true }));\n+\n+// Request logging middleware\n+app.use((req, res, next) => {\n+    const timestamp = new Date().toISOString();\n+    const method = req.method;\n+    const url = req.url;\n+    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;\n+    \n+    console.log(`[${timestamp}] ${method} ${url} - ${ip}`);\n+    next();\n+});\n+\n+// Health check endpoint for Render\n+app.get('/health', async (req, res) => {\n+    try {\n+        const dbHealth = await checkDatabaseHealth();\n+        res.json({\n+            status: 'healthy',\n+            timestamp: new Date().toISOString(),\n+            database: dbHealth,\n+            version: '2.0.0-turso'\n+        });\n+    } catch (error) {\n+        res.status(500).json({\n+            status: 'unhealthy',\n+            error: error.message,\n+            timestamp: new Date().toISOString()\n+        });\n+    }\n+});\n+\n+// Root endpoint\n+app.get('/', (req, res) => {\n+    res.json({\n+        name: 'HustlePlug Pro Validation API',\n+        version: '2.0.0-turso',\n+        status: 'running',\n+        endpoints: {\n+            validate: 'POST /api/validate-key',\n+            health: 'GET /health'\n+        },\n+        timestamp: new Date().toISOString()\n+    });\n+});\n+\n+// Main validation endpoint - maintains exact same path as Vercel\n+app.post('/api/validate-key', async (req, res) => {\n+    try {\n+        // Convert Express req/res to match Vercel serverless function format\n+        await validateKeyHandler(req, res);\n+    } catch (error) {\n+        console.error('Validation endpoint error:', error);\n+        res.status(500).json({\n+            success: false,\n+            error: 'Internal server error',\n+            message: 'Validation service temporarily unavailable'\n+        });\n+    }\n+});\n+\n+// Handle preflight requests for CORS\n+app.options('*', (req, res) => {\n+    res.status(200).end();\n+});\n+\n+// 404 handler\n+app.use('*', (req, res) => {\n+    res.status(404).json({\n+        error: 'Not Found',\n+        message: 'The requested endpoint does not exist',\n+        availableEndpoints: [\n+            'POST /api/validate-key',\n+            'GET /health',\n+            'GET /'\n+        ]\n+    });\n+});\n+\n+// Global error handler\n+app.use((error, req, res, next) => {\n+    console.error('Global error handler:', error);\n+    res.status(500).json({\n+        success: false,\n+        error: 'Internal server error',\n+        message: 'Something went wrong on our end'\n+    });\n+});\n+\n+// Start server\n+async function startServer() {\n+    try {\n+        // Test database connection before starting\n+        console.log('🔄 Testing Turso database connection...');\n+        const dbConnected = await testConnection();\n+        \n+        if (!dbConnected) {\n+            console.error('❌ Failed to connect to Turso database. Please check your credentials.');\n+            process.exit(1);\n+        }\n+        \n+        // Start the server\n+        app.listen(PORT, '0.0.0.0', () => {\n+            console.log(`🚀 HustlePlug Pro Validation API running on port ${PORT}`);\n+            console.log(`📊 Health check: http://localhost:${PORT}/health`);\n+            console.log(`🔑 Validation endpoint: http://localhost:${PORT}/api/validate-key`);\n+            console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);\n+        });\n+        \n+    } catch (error) {\n+        console.error('❌ Failed to start server:', error);\n+        process.exit(1);\n+    }\n+}\n+\n+// Handle graceful shutdown\n+process.on('SIGTERM', () => {\n+    console.log('🔄 SIGTERM received, shutting down gracefully...');\n+    process.exit(0);\n+});\n+\n+process.on('SIGINT', () => {\n+    console.log('🔄 SIGINT received, shutting down gracefully...');\n+    process.exit(0);\n+});\n+\n+// Start the application\n+startServer();", "summary": "Created new Express.js server application to replace Vercel serverless architecture. Includes health checks, CORS, security middleware, and maintains backward compatibility.", "rollback_note": "Delete this file to revert to serverless. Ensure Vercel configuration is restored."}, {"file_path": "vercel-api/db/connection.js", "change_type": "new_file", "git_diff": "--- /dev/null\n+++ b/vercel-api/db/connection.js\n@@ -0,0 +1,59 @@\n+// Turso Database Connection Module\n+// Handles libSQL client setup and connection management\n+\n+import { createClient } from '@libsql/client';\n+import dotenv from 'dotenv';\n+\n+// Load environment variables\n+dotenv.config();\n+\n+// Validate required environment variables\n+const requiredEnvVars = ['TURSO_DATABASE_URL', 'TURSO_AUTH_TOKEN'];\n+const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);\n+\n+if (missingVars.length > 0) {\n+    console.error('❌ Missing required environment variables:', missingVars.join(', '));\n+    console.error('Please check your .env file and ensure all Turso credentials are set.');\n+    process.exit(1);\n+}\n+\n+// Create Turso client\n+export const turso = createClient({\n+    url: process.env.TURSO_DATABASE_URL,\n+    authToken: process.env.TURSO_AUTH_TOKEN,\n+});\n+\n+// Test database connection\n+export async function testConnection() {\n+    try {\n+        const result = await turso.execute('SELECT 1 as test');\n+        console.log('✅ Turso database connection successful');\n+        return true;\n+    } catch (error) {\n+        console.error('❌ Turso database connection failed:', error.message);\n+        return false;\n+    }\n+}\n+\n+// Graceful shutdown\n+export async function closeConnection() {\n+    try {\n+        await turso.close();\n+        console.log('✅ Turso database connection closed');\n+    } catch (error) {\n+        console.error('❌ Error closing Turso connection:', error.message);\n+    }\n+}\n+\n+// Handle process termination\n+process.on('SIGINT', async () => {\n+    console.log('\\n🔄 Gracefully shutting down...');\n+    await closeConnection();\n+    process.exit(0);\n+});\n+\n+process.on('SIGTERM', async () => {\n+    console.log('\\n🔄 Gracefully shutting down...');\n+    await closeConnection();\n+    process.exit(0);\n+});", "summary": "Created Turso database connection module with environment validation, connection testing, and graceful shutdown handling.", "rollback_note": "Safe to delete - only used by new database architecture. No impact on existing Vercel setup."}, {"file_path": "vercel-api/db/queries.js", "change_type": "new_file", "git_diff": "--- /dev/null\n+++ b/vercel-api/db/queries.js\n@@ -0,0 +1,307 @@\n+// Database Query Functions for Turso Integration\n+// Handles all database operations for pro key validation and management\n+\n+import crypto from 'crypto';\n+import { turso } from './connection.js';\n+\n+// Salt for hashing (must match extension salt)\n+const PRO_SALT = 'AgentHustle2024ProSalt!@#$%^&*()_+SecureKey';\n+\n+/**\n+ * Hash a key using the same algorithm as the extension\n+ * @param {string} key - Plain text key\n+ * @param {string} salt - Salt for hashing\n+ * @returns {string} - Hashed key\n+ */\n+export function hashKey(key, salt = PRO_SALT) {\n+    return crypto.createHash('sha256').update(key + salt).digest('hex');\n+}\n+\n+/**\n+ * Validate a pro key against the database\n+ * @param {string} plainKey - Plain text key to validate\n+ * @returns {Promise<Object>} - Validation result\n+ */\n+export async function validate<PERSON><PERSON>(plainKey) {\n+    try {\n+        // Hash the key using the same salt as the extension\n+        const hashedKey = hashKey(plainKey);\n+        \n+        console.log(`🔍 Validating hashed key: ${hashedKey.substring(0, 16)}...`);\n+        \n+        // Query the database for the hashed key\n+        const result = await turso.execute({\n+            sql: `\n+                SELECT \n+                    pk.id,\n+                    pk.key_hash,\n+                    pk.status,\n+                    pk.tier,\n+                    pk.expires_at,\n+                    pk.created_at,\n+                    pk.usage_count,\n+                    pk.notes,\n+                    c.name as customer_name,\n+                    c.email as customer_email\n+                FROM pro_keys pk\n+                LEFT JOIN customers c ON pk.customer_id = c.id\n+                WHERE pk.key_hash = ? AND pk.status = 'active'\n+            `,\n+            args: [hashedKey]\n+        });\n+        \n+        if (result.rows.length === 0) {\n+            console.log(`❌ No active key found for hash: ${hashedKey.substring(0, 16)}...`);\n+            return { isValid: false, keyData: null };\n+        }\n+        \n+        const keyData = result.rows[0];\n+        \n+        // Check if key is expired\n+        if (keyData.expires_at) {\n+            const expirationDate = new Date(keyData.expires_at);\n+            const now = new Date();\n+            \n+            if (now > expirationDate) {\n+                console.log(`⏰ Key expired on: ${expirationDate.toISOString()}`);\n+                return { isValid: false, keyData: keyData, reason: 'expired' };\n+            }\n+        }\n+        \n+        console.log(`✅ Valid key found: ${keyData.customer_name || 'Unknown'} (${keyData.tier})`);\n+        \n+        return {\n+            isValid: true,\n+            keyData: keyData\n+        };\n+        \n+    } catch (error) {\n+        console.error('❌ Database validation error:', error);\n+        throw new Error(`Key validation failed: ${error.message}`);\n+    }\n+}\n+\n+/**\n+ * Log key usage for analytics and monitoring\n+ * @param {number} keyId - Database ID of the key\n+ * @param {string} ipAddress - Client IP address\n+ * @param {string} userAgent - Client user agent\n+ * @param {string} action - Action performed (validate, etc.)\n+ * @returns {Promise<void>}\n+ */\n+export async function logKeyUsage(keyId, ipAddress, userAgent, action = 'validate') {\n+    try {\n+        // Insert usage log\n+        await turso.execute({\n+            sql: `\n+                INSERT INTO key_usage (key_id, ip_address, user_agent, action, used_at)\n+                VALUES (?, ?, ?, ?, datetime('now'))\n+            `,\n+            args: [keyId, ipAddress, userAgent, action]\n+        });\n+        \n+        // Update usage counter on the key\n+        await turso.execute({\n+            sql: `\n+                UPDATE pro_keys \n+                SET usage_count = usage_count + 1, \n+                    last_used = datetime('now')\n+                WHERE id = ?\n+            `,\n+            args: [keyId]\n+        });\n+        \n+        console.log(`📊 Usage logged: Key ID ${keyId}, IP ${ipAddress}`);\n+        \n+    } catch (error) {\n+        console.error('❌ Usage logging error:', error);\n+        throw new Error(`Usage logging failed: ${error.message}`);\n+    }\n+}\n+\n+/**\n+ * Get key statistics and usage information\n+ * @param {string} plainKey - Plain text key\n+ * @returns {Promise<Object>} - Key statistics\n+ */\n+export async function getKeyStats(plainKey) {\n+    try {\n+        const hashedKey = hashKey(plainKey);\n+        \n+        const result = await turso.execute({\n+            sql: `\n+                SELECT \n+                    pk.id,\n+                    pk.tier,\n+                    pk.status,\n+                    pk.usage_count,\n+                    pk.created_at,\n+                    pk.expires_at,\n+                    pk.last_used,\n+                    c.name as customer_name,\n+                    c.email as customer_email,\n+                    COUNT(ku.id) as total_usage_logs\n+                FROM pro_keys pk\n+                LEFT JOIN customers c ON pk.customer_id = c.id\n+                LEFT JOIN key_usage ku ON pk.id = ku.key_id\n+                WHERE pk.key_hash = ?\n+                GROUP BY pk.id\n+            `,\n+            args: [hashedKey]\n+        });\n+        \n+        if (result.rows.length === 0) {\n+            return null;\n+        }\n+        \n+        return result.rows[0];\n+        \n+    } catch (error) {\n+        console.error('❌ Key stats error:', error);\n+        throw new Error(`Key stats retrieval failed: ${error.message}`);\n+    }\n+}\n+\n+/**\n+ * Check database health and connectivity\n+ * @returns {Promise<Object>} - Health status\n+ */\n+export async function checkDatabaseHealth() {\n+    try {\n+        // Test basic connectivity\n+        const connectTest = await turso.execute('SELECT 1 as test');\n+        \n+        // Count total keys\n+        const keyCount = await turso.execute('SELECT COUNT(*) as count FROM pro_keys');\n+        \n+        // Count active keys\n+        const activeKeyCount = await turso.execute(\n+            \"SELECT COUNT(*) as count FROM pro_keys WHERE status = 'active'\"\n+        );\n+        \n+        // Get recent usage\n+        const recentUsage = await turso.execute(\n+            \"SELECT COUNT(*) as count FROM key_usage WHERE used_at > datetime('now', '-24 hours')\"\n+        );\n+        \n+        return {\n+            status: 'healthy',\n+            totalKeys: keyCount.rows[0].count,\n+            activeKeys: activeKeyCount.rows[0].count,\n+            recentUsage24h: recentUsage.rows[0].count,\n+            lastChecked: new Date().toISOString()\n+        };\n+        \n+    } catch (error) {\n+        console.error('❌ Database health check failed:', error);\n+        return {\n+            status: 'unhealthy',\n+            error: error.message,\n+            lastChecked: new Date().toISOString()\n+        };\n+    }\n+}\n+\n+/**\n+ * Get all active keys (admin function)\n+ * @returns {Promise<Array>} - List of active keys\n+ */\n+export async function getAllActiveKeys() {\n+    try {\n+        const result = await turso.execute(`\n+            SELECT \n+                pk.id,\n+                pk.tier,\n+                pk.status,\n+                pk.usage_count,\n+                pk.created_at,\n+                pk.expires_at,\n+                pk.last_used,\n+                pk.notes,\n+                c.name as customer_name,\n+                c.email as customer_email\n+            FROM pro_keys pk\n+            LEFT JOIN customers c ON pk.customer_id = c.id\n+            WHERE pk.status = 'active'\n+            ORDER BY pk.created_at DESC\n+        `);\n+        \n+        return result.rows;\n+        \n+    } catch (error) {\n+        console.error('❌ Get active keys error:', error);\n+        throw new Error(`Failed to retrieve active keys: ${error.message}`);\n+    }\n+}\n+\n+/**\n+ * Create a new pro key\n+ * @param {Object} keyData - Key data object\n+ * @returns {Promise<Object>} - Created key info\n+ */\n+export async function createProKey(keyData) {\n+    try {\n+        const {\n+            plainKey,\n+            customerId,\n+            tier = 'pro',\n+            expiresAt = null,\n+            notes = null\n+        } = keyData;\n+        \n+        const hashedKey = hashKey(plainKey);\n+        \n+        const result = await turso.execute({\n+            sql: `\n+                INSERT INTO pro_keys (key_hash, customer_id, tier, expires_at, notes, status)\n+                VALUES (?, ?, ?, ?, ?, 'active')\n+            `,\n+            args: [hashedKey, customerId, tier, expiresAt, notes]\n+        });\n+        \n+        console.log(`✅ Created new pro key for customer ${customerId}`);\n+        \n+        return {\n+            id: result.lastInsertRowid,\n+            hashedKey: hashedKey,\n+            tier: tier,\n+            expiresAt: expiresAt\n+        };\n+        \n+    } catch (error) {\n+        console.error('❌ Create pro key error:', error);\n+        throw new Error(`Failed to create pro key: ${error.message}`);\n+    }\n+}\n+\n+/**\n+ * Deactivate a pro key\n+ * @param {string} plainKey - Plain text key to deactivate\n+ * @returns {Promise<boolean>} - Success status\n+ */\n+export async function deactivateKey(plainKey) {\n+    try {\n+        const hashedKey = hashKey(plainKey);\n+        \n+        const result = await turso.execute({\n+            sql: `\n+                UPDATE pro_keys \n+                SET status = 'inactive', \n+                    updated_at = datetime('now')\n+                WHERE key_hash = ?\n+            `,\n+            args: [hashedKey]\n+        });\n+        \n+        if (result.changes === 0) {\n+            console.log(`⚠️ No key found to deactivate: ${hashedKey.substring(0, 16)}...`);\n+            return false;\n+        }\n+        \n+        console.log(`🔒 Deactivated key: ${hashedKey.substring(0, 16)}...`);\n+        return true;\n+        \n+    } catch (error) {\n+        console.error('❌ Deactivate key error:', error);\n+        throw new Error(`Failed to deactivate key: ${error.message}`);\n+    }\n+}", "summary": "Created comprehensive database query module with key validation, usage logging, statistics, health checks, and admin functions. Implements secure hashing and proper error handling.", "rollback_note": "Safe to delete - only used by new database architecture. Contains all business logic for database operations."}, {"file_path": "vercel-api/api/validate-key.js", "change_type": "major_refactor", "git_diff": "--- a/vercel-api/api/validate-key.js\n+++ b/vercel-api/api/validate-key.js\n@@ -1,50 +1,196 @@\n-// Enhanced Pro Key Validation API with hardcoded keys\n-// Supports membership tracking with expiration dates and tiers\n-// Works with hashed keys for security\n+// Enhanced Pro Key Validation API with Turso Database\n+// Supports membership tracking with expiration dates, tiers, and usage\n+// Works with hashed keys for security and real database storage\n \n import crypto from 'crypto';\n+import { validate<PERSON>ey, logKeyUsage } from '../db/queries.js';\n \n // Salt for hashing (should match your extension's salt)\n const PRO_SALT = 'AgentHustle2024ProSalt!@#$%^&*()_+SecureKey';\n \n-// Hardcoded pro keys with membership details (hashed)\n-const PRO_KEYS = {\n-    // <PERSON> - <PERSON> (expires June 13, 2026)\n-    'a1b2c3d4e5f6789012345678901234567890abcdef1234567890ab<PERSON>f123456': {\n-        tier: 'premium',\n-        expiresAt: '2026-06-13T23:59:59.000Z',\n-        notes: '<PERSON> - Premium Member'\n-    },\n-    // <PERSON> - Pro (expires December 13, 2025)\n-    'b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a': {\n-        tier: 'pro',\n-        expiresAt: '2025-12-13T23:59:59.000Z',\n-        notes: 'Lisa Chen - Pro Member'\n-    },\n-    // David Wilson - Pro (expires June 13, 2027)\n-    'c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2': {\n-        tier: 'pro',\n-        expiresAt: '2027-06-13T23:59:59.000Z',\n-        notes: 'David Wilson - Pro Member'\n-    }\n-};\n+// Rate limiting mechanism\n+const rateLimitMap = new Map(); // Track requests by IP\n+const RATE_LIMIT_WINDOW = 10000; // 10 seconds\n+const MAX_REQUESTS_PER_WINDOW = 3; // Max 3 requests per 10 seconds per IP\n \n /**\n  * Hash a key using the same algorithm as the extension\n  * @param {string} key - Plain text key\n  * @param {string} salt - Salt for hashing\n  * @returns {string} - Hashed key\n  */\n function hashKey(key, salt = PRO_SALT) {\n     return crypto.createHash('sha256').update(key + salt).digest('hex');\n }\n \n+/**\n+ * Check if request should be rate limited\n+ * @param {string} clientIP - Client IP address\n+ * @returns {boolean} - True if rate limited\n+ */\n+function isRateLimited(clientIP) {\n+    const now = Date.now();\n+    const key = clientIP;\n+    \n+    if (!rateLimitMap.has(key)) {\n+        rateLimitMap.set(key, { count: 1, firstRequest: now });\n+        return false;\n+    }\n+    \n+    const rateData = rateLimitMap.get(key);\n+    \n+    // Reset window if enough time has passed\n+    if (now - rateData.firstRequest > RATE_LIMIT_WINDOW) {\n+        rateLimitMap.set(key, { count: 1, firstRequest: now });\n+        return false;\n+    }\n+    \n+    // Check if limit exceeded\n+    if (rateData.count >= MAX_REQUESTS_PER_WINDOW) {\n+        console.log(`🚫 Rate limit exceeded for IP: ${clientIP} (${rateData.count} requests)`);\n+        return true;\n+    }\n+    \n+    // Increment counter\n+    rateData.count++;\n+    return false;\n+}\n+\n+/**\n+ * Get client IP address from request headers\n+ * @param {Request} req - Express request object\n+ * @returns {string} - Client IP address\n+ */\n+function getClientIP(req) {\n+    // Check various headers for the real IP (considering proxies/CDNs)\n+    const forwarded = req.headers['x-forwarded-for'];\n+    const realIP = req.headers['x-real-ip'];\n+    const cfConnectingIP = req.headers['cf-connecting-ip']; // Cloudflare\n+    \n+    if (forwarded) {\n+        // x-forwarded-for can contain multiple IPs, take the first one\n+        return forwarded.split(',')[0].trim();\n+    }\n+    \n+    return realIP || cfConnectingIP || req.connection?.remoteAddress || req.ip || 'unknown';\n+}\n+\n-export default function handler(req, res) {\n+export default async function handler(req, res) {\n     // Set CORS headers\n     res.setHeader('Access-Control-Allow-Origin', '*');\n     res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');\n@@ -70,35 +216,80 @@ export default function handler(req, res) {\n     \n     try {\n+        // Get client IP and check rate limiting\n+        const clientIP = getClientIP(req);\n+        \n+        if (isRateLimited(clientIP)) {\n+            return res.status(429).json({\n+                success: false,\n+                message: 'Rate limit exceeded. Please wait before making another request.',\n+                rateLimited: true,\n+                retryAfter: Math.ceil(RATE_LIMIT_WINDOW / 1000) // seconds\n+            });\n+        }\n+        \n+        console.log(`🔍 Validation request from IP: ${clientIP}`);\n+        \n         const { key, action } = req.body;\n         \n         if (!key) {\n@@ -115,35 +262,80 @@ export default function handler(req, res) {\n             });\n         }\n         \n-        // Hash the provided key\n-        const hashedKey = hashKey(key);\n-        \n-        // Check if the hashed key exists in our pro keys\n-        const membershipData = PRO_KEYS[hashedKey];\n-        \n-        if (membershipData) {\n-            // Check if membership is expired\n-            const now = new Date();\n-            const expirationDate = new Date(membershipData.expiresAt);\n-            const isExpired = now > expirationDate;\n-            const daysRemaining = Math.max(0, Math.ceil((expirationDate - now) / (1000 * 60 * 60 * 24)));\n+        // Validate the key against database\n+        const result = await validateKey(key);\n+        \n+        if (result.isValid) {\n+            // Try to log successful usage, but don't fail if logging fails\n+            try {\n+                await logKeyUsage(\n+                    result.keyData.id,\n+                    clientIP,\n+                    req.headers['user-agent'] || 'Unknown',\n+                    'validate'\n+                );\n+            } catch (loggingError) {\n+                console.warn('⚠️ Usage logging failed, but continuing with validation:', loggingError.message);\n+            }\n+            \n+            console.log(`✅ Valid key used: ${result.keyData.notes?.substring(0, 30)}... (Usage: ${result.keyData.usage_count + 1})`);\n+            \n+            // Calculate expiration details\n+            const now = new Date();\n+            const expiresAt = result.keyData.expires_at ? new Date(result.keyData.expires_at) : null;\n+            const isExpired = expiresAt ? now > expiresAt : false;\n+            const daysRemaining = expiresAt ? Math.max(0, Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24))) : null;\n             \n             return res.status(200).json({\n                 success: true,\n                 isPro: true,\n                 message: 'Valid pro key',\n                 membershipDetails: {\n-                    status: isExpired ? 'expired' : 'active',\n-                    tier: membershipData.tier,\n-                    usageCount: 1, // This would be tracked in a real database\n+                    status: isExpired ? 'expired' : 'active',\n+                    tier: result.keyData.tier || 'pro',\n+                    usageCount: result.keyData.usage_count + 1,\n                     lastUsed: new Date().toISOString(),\n-                    createdAt: null, // Would be from database\n-                    expiresAt: membershipData.expiresAt,\n+                    createdAt: result.keyData.created_at || null,\n+                    expiresAt: result.keyData.expires_at || null,\n                     daysRemaining: daysRemaining,\n                     isExpired: isExpired,\n-                    notes: membershipData.notes\n+                    notes: result.keyData.notes\n                 }\n             });\n         } else {\n+            console.log(`❌ Invalid key attempt from IP: ${clientIP}`);\n+            \n             return res.status(200).json({\n                 success: true,\n                 isPro: false,\n@@ -152,7 +344,17 @@ export default function handler(req, res) {\n         }\n         \n     } catch (error) {\n-        console.error('Validation error:', error);\n+        console.error('❌ Validation error:', error);\n         \n         return res.status(500).json({\n             success: false,\n@@ -160,4 +362,12 @@ export default function handler(req, res) {\n         });\n     }\n }\n+\n+// Clean up old rate limit entries periodically\n+setInterval(() => {\n+    const now = Date.now();\n+    for (const [key, data] of rateLimitMap.entries()) {\n+        if (now - data.firstRequest > RATE_LIMIT_WINDOW * 2) {\n+            rateLimitMap.delete(key);\n+        }\n+    }\n+}, RATE_LIMIT_WINDOW);", "summary": "Major refactor from hardcoded keys to database integration. Added rate limiting, IP tracking, usage logging, and comprehensive error handling. Maintains backward compatibility with Chrome extension.", "rollback_note": "CRITICAL: This breaks hardcoded key validation. Rollback requires restoring hardcoded PRO_KEYS object and removing database imports."}, {"file_path": "vercel-api/render.yaml", "change_type": "new_file", "git_diff": "--- /dev/null\n+++ b/vercel-api/render.yaml\n@@ -0,0 +1,47 @@\n+# Render.com Deployment Configuration for HustlePlug Pro Validation API\n+# This replaces Vercel deployment with Render for better database integration\n+\n+services:\n+  - type: web\n+    name: hustleplug-pro-api\n+    runtime: node\n+    plan: free  # Can upgrade to starter ($7/month) for better performance\n+    region: oregon  # Choose region closest to your users\n+    buildCommand: npm install\n+    startCommand: npm start\n+    \n+    # Environment variables (set these in Render dashboard)\n+    envVars:\n+      - key: NODE_ENV\n+        value: production\n+      - key: PORT\n+        value: 3000\n+      - key: TURSO_DATABASE_URL\n+        sync: false  # Set manually in dashboard\n+      - key: TURSO_AUTH_TOKEN\n+        sync: false  # Set manually in dashboard\n+    \n+    # Health check configuration\n+    healthCheckPath: /health\n+    \n+    # Auto-deploy from GitHub\n+    repo: https://github.com/yourusername/hustleplug  # Update with your repo\n+    branch: main\n+    rootDir: vercel-api  # Deploy only the API directory\n+    \n+    # Resource limits\n+    disk:\n+      name: hustleplug-disk\n+      size: 1GB\n+      \n+    # Custom domains (optional)\n+    # domains:\n+    #   - hustleplug-api.yourdomain.com\n+    \n+    # Build settings\n+    buildFilter:\n+      paths:\n+        - vercel-api/**\n+      ignoredPaths:\n+        - README.md\n+        - \"*.md\"", "summary": "Created Render deployment configuration to replace Vercel. Includes environment variables, health checks, auto-deploy from GitHub, and resource limits.", "rollback_note": "Safe to delete - only used for Render deployment. Does not affect existing Vercel setup."}, {"file_path": "vercel-api/.gitignore", "change_type": "new_file", "git_diff": "--- /dev/null\n+++ b/vercel-api/.gitignore\n@@ -0,0 +1,65 @@\n+# Dependencies\n+node_modules/\n+npm-debug.log*\n+yarn-debug.log*\n+yarn-error.log*\n+\n+# Environment variables\n+.env\n+.env.local\n+.env.development.local\n+.env.test.local\n+.env.production.local\n+\n+# Runtime data\n+pids\n+*.pid\n+*.seed\n+*.pid.lock\n+\n+# Coverage directory used by tools like istanbul\n+coverage/\n+*.lcov\n+\n+# nyc test coverage\n+.nyc_output\n+\n+# Grunt intermediate storage\n+.grunt\n+\n+# Bower dependency directory\n+bower_components\n+\n+# node-waf configuration\n+.lock-wscript\n+\n+# Compiled binary addons\n+build/Release\n+\n+# Dependency directories\n+jspm_packages/\n+\n+# TypeScript cache\n+*.tsbuildinfo\n+\n+# Optional npm cache directory\n+.npm\n+\n+# Optional eslint cache\n+.eslintcache\n+\n+# Optional REPL history\n+.node_repl_history\n+\n+# Output of 'npm pack'\n+*.tgz\n+\n+# Yarn Integrity file\n+.yarn-integrity\n+\n+# parcel-bundler cache\n+.cache\n+.parcel-cache\n+\n+# Stores VSCode versions used for testing VSCode extensions\n+.vscode-test", "summary": "Added comprehensive .gitignore file to exclude node_modules, environment variables, and build artifacts from version control.", "rollback_note": "Safe to delete - only affects what files are tracked in git. No functional impact."}], "database_schema": {"tables_created": ["customers", "pro_keys", "key_usage"], "relationships": "Normalized schema with foreign keys between customers and pro_keys, pro_keys and key_usage", "indexes": "Performance indexes on key_hash, customer_id, and used_at columns"}, "migration_data": {"customers_migrated": 3, "keys_migrated": 6, "test_keys_included": true, "hash_algorithm": "SHA256 with salt: AgentHustle2024ProSalt!@#$%^&*()_+SecureKey"}, "deployment_status": {"architecture_change": "Vercel Serverless → Render Express.js", "database_change": "Hardcoded Keys → Turso Database", "backward_compatibility": "Maintained - Chrome extension works without changes", "endpoint_compatibility": "POST /api/validate-key maintained exactly"}, "testing_completed": ["Database connection tests", "Key validation tests", "Rate limiting tests", "Health endpoint tests", "Browser simulation tests", "Environment variable tests"], "issues_resolved": ["Turso CLI location (WSL vs Windows)", "Missing async keyword in handler", "Directory navigation issues", "IPv6 connection problems", "Environment variable loading"], "rollback_instructions": {"critical_files": ["vercel-api/api/validate-key.js - MUST restore hardcoded PRO_KEYS", "vercel-api/package.json - Remove new dependencies", "vercel-api/app.js - Delete entirely"], "safe_to_delete": ["vercel-api/db/ directory", "vercel-api/render.yaml", "vercel-api/.gitignore", "All test-*.js files"], "environment_impact": "Rollback requires removing Turso environment variables and restoring Vercel configuration"}, "performance_impact": {"positive": ["Real database with proper indexing", "Usage tracking and analytics", "Rate limiting protection", "Health monitoring", "Graceful error handling"], "considerations": ["Database latency vs hardcoded lookup", "Network dependency on Turso", "Memory usage for rate limiting"]}, "security_improvements": ["Rate limiting by IP address", "Helmet security middleware", "Environment variable validation", "SQL injection prevention with parameterized queries", "Graceful error handling without data exposure"]}