/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #141021; /* Deep Violet / Midnight Purple */
    color: #F9F9F9; /* High Contrast White */
    line-height: 1.6;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

.container {
    width: 520px;
    min-height: 680px;
    background: #141021;
    border-radius: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Styles for the main content area to make footer stick to bottom */
.main-content-area {
    flex-grow: 1; /* Allows this area to expand and push footer down */
    overflow-y: auto; /* Allows content within this area to scroll if it's too tall */
    display: flex; /* Added to allow its children (sections) to be managed if needed, e.g. centering */
    flex-direction: column; /* Sections stack vertically */
    min-height: 0; /* Important for flex children that might scroll or have min-heights themselves */
    /* padding-bottom: 20px; /* Optional: if sections need more space from footer */
                           /* Current sections have their own padding, so this might not be needed. */
}

/* Header Styles */
.header {
    background: linear-gradient(145deg, #3F2B96, #A8C0FF);
    color: #F9F9F9;
    padding: 24px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 4s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 12px;
    position: relative;
    z-index: 1;
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.logo-text {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-text h1 {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
}

.pro-badge {
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 1px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tagline {
    font-size: 15px;
    opacity: 0.9;
    font-weight: 400;
    position: relative;
    z-index: 1;
    margin-top: 4px;
}

/* Section Styles */
.section {
    padding: 20px;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #F9F9F9;
    display: flex;
    align-items: center;
    gap: 8px;
}

.about-content, .help-content {
    padding: 0 10px; /* Add some padding for better spacing */
}

.about-content h2,
.help-content h4 {
    color: #A8C0FF; /* Use a distinct color for titles */
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.about-content h2:first-child,
.help-content h4:first-child {
    margin-top: 0;
}

.about-content p,
.help-content p {
    color: #B2AFC5; /* Lighter text for readability */
    margin-bottom: 1em;
}

.about-content .logo {
    text-align: center;
    margin-bottom: 1em;
}

.about-content .logo svg {
    width: 60px;
    height: 60px;
}

.about-content h2 {
    text-align: center;
    font-size: 20px;
    margin-bottom: 0.25em;
}

.about-content .version {
    text-align: center;
    font-size: 14px;
    color: #B2AFC5;
    margin-bottom: 1em;
}

.about-content .powered-by {
    text-align: center;
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 0.25em;
}

.about-content p {
    text-align: center;
}

/* Input Styles */
.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
}

input[type="password"], input[type="text"], textarea {
    flex: 1;
    padding: 14px 16px;
    border: 2px solid #2C2738;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #1D1A2A;
    color: #F9F9F9;
    font-family: inherit;
}

input[type="password"]:focus, input[type="text"]:focus, textarea:focus {
    outline: none;
    border-color: #5BA9F9;
    background: #1D1A2A;
    box-shadow: 0 0 0 3px rgba(91, 169, 249, 0.15);
}

input[type="password"]::placeholder, input[type="text"]::placeholder, textarea::placeholder {
    color: #B2AFC5;
}

textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    width: 100%;
}

/* Button Styles */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
    color: #F9F9F9;
    border: 1px solid rgba(91, 169, 249, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(91, 169, 249, 0.3);
}

.btn-secondary {
    background: #2C2738;
    color: #B2AFC5;
    border: 1px solid #2C2738;
}

.btn-secondary:hover {
    background: #3A3548;
    color: #F9F9F9;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #B2AFC5;
    border: 2px solid #2C2738;
}

.btn-outline:hover {
    background: #2C2738;
    color: #F9F9F9;
    border-color: #3A3548;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
}

.btn-full {
    width: 100%;
}

.btn-icon {
    font-size: 16px;
}

/* API Status */
.api-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #1D1A2A;
    border-radius: 10px;
    border: 1px solid #2C2738;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF5C5C;
    animation: blink 2s infinite;
}

.status-indicator.connected {
    background: #28a745;
    animation: none;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.status-text {
    color: #B2AFC5;
    font-size: 14px;
}

/* Action Grid */
.action-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 18px 20px;
    background: #1D1A2A;
    border: 2px solid #2C2738;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(91, 169, 249, 0.05), transparent);
    transition: left 0.6s;
}

.action-btn:hover {
    transform: translateY(-3px);
    border-color: #5BA9F9;
    box-shadow: 0 8px 25px rgba(91, 169, 249, 0.2);
}

.action-btn:hover::before {
    left: 100%;
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.action-icon.analyze-selection {
    background: linear-gradient(135deg, #FFD84D, #F4BD61);
    color: #141021;
}

.action-icon.analyze-page {
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    color: #F9F9F9;
}

.action-icon.custom-analysis {
    background: linear-gradient(135deg, #FF5C5C, #FF8A80);
    color: #F9F9F9;
}

.action-icon.crypto-analysis {
    background: linear-gradient(135deg, #F4BD61, #FFD84D);
    color: #141021;
}

.action-text {
    flex: 1;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 4px;
}

.action-desc {
    font-size: 13px;
    color: #B2AFC5;
    line-height: 1.4;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #F9F9F9;
    font-size: 14px;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 12px 16px;
    background: #1D1A2A;
    border-radius: 10px;
    border: 2px solid #2C2738;
    transition: all 0.3s ease;
    color: #B2AFC5;
}

.radio-label:hover {
    border-color: #5BA9F9;
    color: #F9F9F9;
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #2C2738;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: #5BA9F9;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s ease;
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: #5BA9F9;
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

.radio-label input[type="radio"]:checked {
    color: #F9F9F9;
}

/* Results Styles */
.results-container {
    max-height: 300px;
    overflow-y: auto;
    padding: 20px;
    background: #1D1A2A;
    border-radius: 12px;
    border: 2px solid #2C2738;
    margin-bottom: 16px;
    color: #F9F9F9;
    line-height: 1.6;
}

.results-container::-webkit-scrollbar {
    width: 6px;
}

.results-container::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb:hover {
    background: #4A9AE8;
}

.results-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* Loading Styles */
.loading-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #2C2738;
    border-top: 4px solid #5BA9F9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text h3 {
    color: #F9F9F9;
    margin-bottom: 8px;
    font-size: 18px;
}

.loading-text p {
    color: #B2AFC5;
    margin-bottom: 20px;
}

.loading-progress {
    max-width: 200px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #2C2738;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #5BA9F9, #FFD84D);
    border-radius: 3px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Footer Styles */
.footer {
    padding: 20px;
    border-top: 1px solid #2C2738;
    background: #141021;
    flex-shrink: 0; /* Prevents footer from shrinking if content is too large */
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 24px;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #B2AFC5;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: #F9F9F9;
    background: #1D1A2A;
    transform: translateY(-1px);
}

.footer-links a svg {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.footer-links a:hover svg {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 540px) {
    .container {
        width: 100%;
        min-width: 450px;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        gap: 16px;
    }
    
    .footer-links a {
        font-size: 12px;
        padding: 6px 8px;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }

/* Error and Success Notifications */
.error-notification, .success-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from { transform: translateX(-50%) translateY(-100%); }
    to { transform: translateX(-50%) translateY(0); }
}

.error-notification > div {
    background: linear-gradient(135deg, #FF5C5C, #FF8A80) !important;
    border: 1px solid rgba(255, 92, 92, 0.3);
    box-shadow: 0 8px 25px rgba(255, 92, 92, 0.3);
}

.success-notification > div {
    background: linear-gradient(135deg, #28a745, #34ce57) !important;
    border: 1px solid rgba(40, 167, 69, 0.3);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

/* Analysis History Styles */
.history-item {
    background: #1D1A2A;
    border: 1px solid #2C2738;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 160px; /* Ensure cards have minimum height to use more space */
    display: flex;
    flex-direction: column;
}

.history-item:hover {
    transform: translateY(-2px);
    background: #252133;
    border-color: #5BA9F9;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 16px;
}

.history-item-title {
    font-weight: 600;
    color: #F9F9F9;
    font-size: 15px;
    flex: 1;
    line-height: 1.3;
}

.history-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    flex-shrink: 0;
}

.history-item-date {
    font-size: 12px;
    color: #B2AFC5;
    white-space: nowrap;
}

.history-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.delete-btn {
    background: none;
    border: none;
    color: #B2AFC5;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.delete-btn:hover {
    color: #FF5C5C;
    background: rgba(255, 92, 92, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

.delete-btn svg {
    transition: all 0.2s ease;
}

.history-item-summary {
    font-size: 14px;
    color: #B2AFC5;
    line-height: 1.5;
    flex: 1; /* Allow summary to grow and fill available space */
    
    /* Truncate text after 6 lines to use more space */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 6; /* increased from 3 to 6 lines */
    -webkit-box-orient: vertical;
    margin-bottom: 16px; /* Add more space before footer */
}

.history-item-footer {
    margin-top: 12px;
    text-align: right;
}

.view-details-link {
    font-size: 13px;
    font-weight: 600;
    color: #5BA9F9;
    text-decoration: none;
    transition: color 0.3s ease;
}

.history-item:hover .view-details-link {
    color: #FFD84D;
}

/* New Grid Layout for History */
.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    grid-auto-rows: minmax(160px, auto);
    padding: 16px;
    margin: 0;
    background: transparent;
    border: none;
    max-height: none;
    height: auto;
    overflow: visible;
}

.history-grid .history-item {
    margin-bottom: 0; /* Remove bottom margin as gap is used */
}

/* Pro Upgrade Section Styles */
.upgrade-content {
    text-align: center;
}

.upgrade-hero {
    margin-bottom: 30px;
}

.upgrade-icon {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.upgrade-hero h2 {
    font-size: 24px;
    font-weight: 700;
    color: #F9F9F9;
    margin-bottom: 8px;
}

.upgrade-hero p {
    font-size: 16px;
    color: #B2AFC5;
    margin-bottom: 0;
}

.upgrade-features {
    margin-bottom: 30px;
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 24px;
    flex-shrink: 0;
    margin-top: 2px;
}

.feature-text {
    flex: 1;
}

.feature-text strong {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 4px;
}

.feature-text p {
    font-size: 14px;
    color: #B2AFC5;
    margin: 0;
    line-height: 1.4;
}

.pro-key-form {
    margin-bottom: 24px;
}

.pro-key-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #F9F9F9;
    font-size: 16px;
    transition: all 0.3s ease;
}

.pro-key-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(168, 192, 255, 0.1);
}

.pro-key-input::placeholder {
    color: #B2AFC5;
}

.key-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 14px;
}

.key-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6B7280;
    flex-shrink: 0;
}

.key-status.validating .status-indicator {
    background: #FFD84D;
    animation: blink 1s infinite;
}

.key-status.valid .status-indicator {
    background: #10B981;
}

.key-status.invalid .status-indicator {
    background: #EF4444;
}

.key-status .status-text {
    color: #B2AFC5;
}

.key-status.valid .status-text {
    color: #10B981;
}

.key-status.invalid .status-text {
    color: #EF4444;
}

.upgrade-footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.upgrade-footer p {
    font-size: 14px;
    color: #B2AFC5;
    margin: 0;
}

.upgrade-footer a {
    color: #5BA9F9;
    text-decoration: none;
    font-weight: 500;
}

.upgrade-footer a:hover {
    text-decoration: underline;
}

/* Enhanced Pro Features Styles */
.upgrade-stats {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(168, 192, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: #A8C0FF;
    border: 1px solid rgba(168, 192, 255, 0.2);
    white-space: nowrap;
}

.feature-item.featured {
    background: linear-gradient(135deg, rgba(168, 192, 255, 0.1), rgba(255, 216, 77, 0.05));
    border: 1px solid rgba(168, 192, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.feature-item.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #A8C0FF, #FFD84D);
}

.feature-highlight {
    display: inline-block;
    background: linear-gradient(135deg, #A8C0FF, #FFD84D);
    color: #141021;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 6px;
}

.social-proof {
    margin-bottom: 16px;
    padding: 16px;
    background: rgba(168, 192, 255, 0.05);
    border-radius: 12px;
    border-left: 3px solid #A8C0FF;
}

.testimonial {
    font-style: italic;
    color: #B2AFC5;
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.cta-section {
    text-align: center;
}

.cta-text {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 8px;
}

/* ============= PROMPT MANAGEMENT STYLES ============= */

/* Prompt Management Controls */
.prompt-controls {
    margin-bottom: 20px;
}

.control-row {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.12);
}

.sort-select {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
    min-width: 120px;
}

.sort-select:focus {
    outline: none;
    border-color: #A8C0FF;
}

.sort-select option {
    background: #2A2C3D;
    color: #F9F9F9;
}

.sort-select option:checked,
.sort-select option:hover {
    background: #5BA9F9;
    color: #F9F9F9;
}

/* Tag Filters */
.filter-tags {
    margin-top: 8px;
}

.tag-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.tag-filter {
    padding: 4px 10px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    color: #B2AFC5;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-filter:hover {
    background: rgba(255, 255, 255, 0.12);
    color: #F9F9F9;
}

.tag-filter.active {
    background: #5BA9F9;
    border-color: #5BA9F9;
    color: white;
}

/* Prompt List */
.prompt-list {
    max-height: none; /* Remove height constraint to use full available space */
    overflow-y: visible; /* Remove scrollbar - show all content */
    margin-bottom: 16px;
    padding-right: 8px; /* Keep padding for consistency */
    height: auto; /* Allow natural height expansion */
}

.prompt-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    margin-right: 4px; /* Additional margin from scrollbar */
    transition: all 0.3s ease;
}

.prompt-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-1px);
}

.prompt-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.prompt-title {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    min-width: 0;
}

.usage-count {
    font-size: 11px;
    font-weight: 400;
    color: #B2AFC5;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
}

.prompt-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.btn-icon:hover::before {
    left: 100%;
}

.btn-icon:focus {
    outline: 2px solid #5BA9F9;
    outline-offset: 2px;
}

.btn-icon.primary:hover {
    background: rgba(91, 169, 249, 0.3);
    color: #A8C0FF;
}

.btn-icon.copy:hover {
    background: rgba(16, 185, 129, 0.3);
    color: #6EE7B7;
}

.btn-icon.pin:hover {
    background: rgba(168, 85, 247, 0.3);
    color: #C4B5FD;
}

.btn-icon.edit:hover {
    background: rgba(59, 130, 246, 0.3);
    color: #93C5FD;
}

.btn-icon.delete:hover {
    background: rgba(239, 68, 68, 0.3);
    color: #FCA5A5;
}

.prompt-content {
    font-size: 14px;
    color: #B2AFC5;
    line-height: 1.4;
    margin-bottom: 16px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    cursor: help;
    transition: all 0.2s ease;
    position: relative;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.prompt-content:hover {
    background: rgba(0, 0, 0, 0.3);
    color: #D1D5DB;
}

.prompt-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.prompt-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(91, 169, 249, 0.2);
    color: #5BA9F9;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    outline: none;
}

.tag:hover {
    background: rgba(91, 169, 249, 0.3);
    color: #A8C0FF;
    transform: translateY(-1px);
}

.tag:focus {
    outline: 2px solid #5BA9F9;
    outline-offset: 2px;
}

.tag:active {
    transform: translateY(0);
}

.prompt-date {
    color: #6B7280;
}

.no-prompts {
    text-align: center;
    color: #B2AFC5;
    padding: 40px 20px;
    font-style: italic;
}

/* Built-in prompt styling */
.prompt-item.built-in-prompt {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.prompt-item.built-in-prompt:hover {
    border-left-color: #34ce57;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.built-in-badge {
    display: inline-block;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    font-size: 9px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* Restore button styling */
#restoreBuiltInPrompts {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#restoreBuiltInPrompts:hover {
    background: linear-gradient(135deg, #34ce57, #17a2b8);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

/* Prompt Actions */
.prompt-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Custom Analysis Form */
.prompt-selector {
    display: flex;
    gap: 8px;
    align-items: center;
}

.prompt-selector select {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
}

.prompt-selector select:focus {
    outline: none;
    border-color: #A8C0FF;
}

.prompt-selector select option {
    background: #2A2C3D;
    color: #F9F9F9;
}

.prompt-selector select option:checked,
.prompt-selector select option:hover {
    background: #5BA9F9;
    color: #F9F9F9;
}

.prompt-selector select optgroup {
    background: #1E1F2A;
    color: #B2AFC5;
    font-weight: 600;
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-start;
    margin-top: 20px;
}

.form-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
    padding: 20px;
    box-sizing: border-box;
}

.modal-content {
    background: linear-gradient(135deg, #2A2C3D 0%, #1E1F2A 100%);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    display: flex;
    flex-direction: column;
    margin: auto;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #F9F9F9;
}

.modal-body {
    padding: 24px;
    max-height: 450px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #F9F9F9;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: transparent;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-custom::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: #5BA9F9;
    border-color: #5BA9F9;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    opacity: 1;
}

.checkbox-label:hover .checkbox-custom {
    border-color: #5BA9F9;
}

/* Action Icons */
.action-icon.manage-prompts {
    background: linear-gradient(135deg, #FF6B6B 0%, #FFD93D 100%);
}

/* ============= PAGINATION STYLES ============= */

.pagination-wrapper {
    margin-top: 16px;
    margin-bottom: 8px;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    padding: 12px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
    font-size: 14px;
    color: #B2AFC5;
    white-space: nowrap;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

.pagination-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #F9F9F9;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: #5BA9F9;
    border-color: #5BA9F9;
    color: white;
    font-weight: 600;
}

.pagination-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    color: #6B7280;
}

.pagination-btn.page-number {
    min-width: 32px;
    padding: 6px 8px;
}

.pagination-ellipsis {
    color: #B2AFC5;
    padding: 6px 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

/* Responsive pagination */
@media (max-width: 540px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .pagination-info {
        text-align: center;
        order: 2;
    }
    
    .pagination-controls {
        justify-content: center;
        order: 1;
    }
    
    .pagination-btn {
        font-size: 12px;
        padding: 4px 8px;
        min-width: 28px;
        height: 28px;
    }
    
    .pagination-btn.page-number {
        min-width: 28px;
        padding: 4px 6px;
    }
}

/* Telegram Integration Styles */
.settings-content {
    padding: 0;
}

.settings-group {
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden; /* Prevent content overflow */
}

.settings-group-header h4 {
    color: #A8C0FF;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-description {
    color: #B2AFC5;
    font-size: 14px;
    margin-bottom: 16px;
}

.pro-upgrade-notice {
    text-align: center;
    padding: 24px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.pro-upgrade-notice .upgrade-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.pro-upgrade-notice h4 {
    color: #A8C0FF;
    font-size: 18px;
    margin-bottom: 8px;
}

.pro-upgrade-notice p {
    color: #B2AFC5;
    margin-bottom: 16px;
}

.telegram-configured {
    padding: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.config-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.config-status .status-indicator.connected {
    background: #28a745;
    animation: none;
}

.config-status .status-text {
    color: #28a745;
    font-weight: 500;
}

.config-details {
    margin-bottom: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 40px; /* Ensure consistent height */
    gap: 12px; /* Add gap between label and value */
}

.config-item:last-child {
    border-bottom: none;
}

.config-item label {
    color: #B2AFC5;
    font-size: 14px;
    font-weight: 500;
    flex-shrink: 0; /* Prevent label from shrinking */
    min-width: 80px; /* Ensure minimum width for labels */
}

.masked-value {
    color: #F9F9F9;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    background: rgba(255, 255, 255, 0.05);
    padding: 4px 8px;
    border-radius: 6px;
    word-break: break-all; /* Break long text */
    overflow-wrap: break-word; /* Wrap long words */
    max-width: 300px; /* Limit maximum width */
    text-align: right; /* Align text to right */
    flex: 1; /* Take remaining space */
    min-width: 0; /* Allow shrinking */
}

.config-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.telegram-setup {
    padding: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.setup-instructions {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(168, 192, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid #A8C0FF;
}

.setup-instructions h5 {
    color: #A8C0FF;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
}

.setup-instructions ol {
    color: #B2AFC5;
    font-size: 14px;
    line-height: 1.6;
    padding-left: 20px;
}

.setup-instructions li {
    margin-bottom: 8px;
}

.setup-instructions a {
    color: #A8C0FF;
    text-decoration: none;
}

.setup-instructions a:hover {
    text-decoration: underline;
}

.setup-instructions code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.setup-form .form-group {
    margin-bottom: 16px;
}

.setup-form .form-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box; /* Include padding in width calculation */
}

.setup-form .form-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(168, 192, 255, 0.1);
}

.setup-form .form-input::placeholder {
    color: #666;
}

.form-hint {
    display: block;
    color: #888;
    font-size: 12px;
    margin-top: 4px;
    word-wrap: break-word; /* Wrap long hint text */
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 16px;
}

.btn-danger {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: #fff;
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(145deg, #c82333, #bd2130);
    transform: translateY(-1px);
}

.telegram-send-btn {
    background: rgba(0, 136, 204, 0.1);
    border: 1px solid rgba(0, 136, 204, 0.3);
    color: #0088cc;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.telegram-send-btn:hover {
    background: rgba(0, 136, 204, 0.2);
    border-color: rgba(0, 136, 204, 0.5);
    transform: translateY(-1px);
}

.telegram-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.telegram-send-btn svg {
    width: 14px;
    height: 14px;
    stroke: currentColor;
}

/* Discord Send Button Styles */
.discord-send-btn {
    background: rgba(88, 101, 242, 0.1);
    border: 1px solid rgba(88, 101, 242, 0.3);
    color: #5865F2;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.discord-send-btn:hover {
    background: rgba(88, 101, 242, 0.2);
    border-color: rgba(88, 101, 242, 0.5);
    transform: translateY(-1px);
}

.discord-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.discord-send-btn svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

/* Discord Configuration Styles */
.discord-configured {
    padding: 16px;
    overflow: hidden;
}

.discord-setup {
    padding: 16px;
    overflow: hidden;
}

.history-item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 8px;
    color: #dc3545;
    font-size: 14px;
}

.error-icon {
    font-size: 16px;
}

/* Pro Feature Section Styling */
.pro-feature-section {
    position: relative;
}

.pro-feature-section::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(145deg, rgba(168, 192, 255, 0.2), rgba(63, 43, 150, 0.2));
    border-radius: 13px;
    z-index: -1;
}

/* Auto-Send Feature Styles */
.auto-send-section {
    margin: 16px 0;
    padding: 16px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.auto-send-header {
    margin-bottom: 16px;
}

.auto-send-header h5 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.auto-send-description {
    color: #ccc;
    font-size: 14px;
    margin: 0;
    line-height: 1.4;
}

.auto-send-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background: #fff;
    transition: all 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background: linear-gradient(145deg, #28a745, #20c997);
    border-color: #28a745;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    background: rgba(255, 255, 255, 0.3);
}

.toggle-switch input:checked:hover + .toggle-slider {
    background: linear-gradient(145deg, #20c997, #17a2b8);
}

.toggle-label {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
}

.auto-send-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.auto-send-status.enabled {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.auto-send-status.disabled {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.auto-send-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    margin-bottom: 8px;
}

.warning-icon {
    font-size: 16px;
    color: #ffc107;
}

.warning-text {
    color: #ffc107;
    font-size: 13px;
    font-weight: 500;
}

.auto-send-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: 8px;
    margin-bottom: 8px;
}

.info-icon {
    font-size: 14px;
    color: #17a2b8;
}

.info-text {
    color: #17a2b8;
    font-size: 12px;
    font-weight: 500;
}

/* Responsive adjustments for Telegram buttons */
@media (max-width: 540px) {
    .config-actions {
        flex-direction: column;
    }
    
    .config-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 12px;
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    .history-item-actions {
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .telegram-send-btn,
    .discord-send-btn {
        min-width: 28px;
        height: 28px;
        padding: 4px 6px;
    }
    
    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .config-item label {
        min-width: auto;
    }
    
    .masked-value {
        max-width: 100%;
        text-align: left;
        width: 100%;
    }
    
    .auto-send-toggle {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .auto-send-section {
        padding: 12px;
    }
    
    .auto-send-header h5 {
        font-size: 15px;
    }
    
    .auto-send-description {
        font-size: 13px;
    }
    
    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .history-item-meta {
        align-items: flex-start;
        width: 100%;
    }
    
    .history-item-date {
        margin-bottom: 4px;
    }
    
    .history-item-actions {
        justify-content: flex-start;
    }
}

/* Membership Information Styles */
.membership-section {
    border: 1px solid rgba(168, 192, 255, 0.2);
    border-radius: 12px;
    background: rgba(168, 192, 255, 0.05);
}

.membership-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #5BA9F9;
}

.membership-status.regular {
    border-left-color: #B2AFC5;
}

.membership-status.expired {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.membership-status.invalid {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.membership-status.active {
    border-left-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.membership-status.error {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.status-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.status-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.status-info h5 {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin: 0 0 4px 0;
}

.status-description {
    font-size: 14px;
    color: #B2AFC5;
    margin: 0;
}

.membership-details {
    margin: 16px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-size: 14px;
    color: #B2AFC5;
    font-weight: 500;
}

.detail-item span {
    font-size: 14px;
    color: #F9F9F9;
    font-weight: 600;
}

.tier-badge {
    background: linear-gradient(145deg, #3F2B96, #A8C0FF);
    color: #F9F9F9;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.expiry-date {
    color: #A8C0FF !important;
}

.days-remaining {
    color: #4CAF50 !important;
}

.days-remaining.warning {
    color: #FFD84D !important;
}

.membership-notes {
    color: #B2AFC5 !important;
    font-style: italic;
}

.expiration-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 216, 77, 0.1);
    border: 1px solid rgba(255, 216, 77, 0.3);
    border-radius: 6px;
    margin: 16px 0;
}

.expiration-warning .warning-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.expiration-warning .warning-text {
    font-size: 14px;
    color: #FFD84D;
    font-weight: 500;
}

.legacy-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(178, 175, 197, 0.1);
    border: 1px solid rgba(178, 175, 197, 0.3);
    border-radius: 6px;
    margin: 16px 0;
}

.legacy-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.legacy-text {
    font-size: 14px;
    color: #B2AFC5;
    font-style: italic;
}

.membership-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.membership-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Responsive adjustments for membership section */
@media (max-width: 540px) {
    .membership-details {
        margin: 12px 0;
        padding: 8px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 6px 0;
    }
    
    .detail-item label {
        font-size: 13px;
    }
    
    .detail-item span {
        font-size: 13px;
    }
    
    .membership-actions {
        flex-direction: column;
    }
    
    .membership-actions .btn {
        width: 100%;
        min-width: auto;
    }
    
    .status-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .status-icon {
        font-size: 20px;
    }
}

/* Key Management Modal Styles */
.key-management-content {
    padding: 0;
    max-width: 100%;
    overflow: hidden;
}

.current-key-section,
.key-actions-section {
    margin-bottom: 24px;
}

.current-key-section h4,
.key-actions-section h4 {
    color: #A8C0FF;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(168, 192, 255, 0.2);
    padding-bottom: 8px;
}

.current-key-display {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    word-wrap: break-word;
}

.key-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    overflow: hidden;
    min-width: 0;
}

.key-info label {
    font-size: 14px;
    color: #B2AFC5;
    font-weight: 500;
}

.masked-key {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #F9F9F9;
    background: rgba(0, 0, 0, 0.3);
    padding: 4px 8px;
    border-radius: 4px;
    letter-spacing: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    flex-shrink: 1;
}

.key-status-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    overflow: hidden;
    min-width: 0;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-badge.active {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-badge.invalid {
    background: rgba(255, 107, 107, 0.2);
    color: #FF6B6B;
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.key-expires {
    font-size: 12px;
    color: #A8C0FF;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
}

.key-action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.key-action-buttons .btn {
    flex: 1;
    min-width: 120px;
}

.key-change-form {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 16px;
}

.key-change-form h4 {
    color: #A8C0FF;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
}

.key-change-form .form-input {
    width: 100%;
    margin-bottom: 8px;
}

.key-change-form .key-status {
    margin-bottom: 16px;
}

.key-change-form .form-actions {
    display: flex;
    gap: 8px;
}

.key-change-form .form-actions .btn {
    flex: 1;
}

/* Responsive adjustments for key management modal */
@media (max-width: 540px) {
    .key-action-buttons {
        flex-direction: column;
    }
    
    .key-action-buttons .btn {
        width: 100%;
        min-width: auto;
    }
    
    .key-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .masked-key {
        max-width: 100%;
        word-break: break-all;
        white-space: normal;
    }
    
    .key-status-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .key-change-form .form-actions {
        flex-direction: column;
    }
    
    .key-change-form .form-actions .btn {
        width: 100%;
    }
} 

/* Template prompt styling */
.prompt-item.template-prompt {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.prompt-item.template-prompt:hover {
    border-left-color: #138496;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.15);
}

.template-badge {
    display: inline-block;
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    font-size: 9px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}