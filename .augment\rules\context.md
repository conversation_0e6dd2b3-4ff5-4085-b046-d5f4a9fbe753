---
type: "agent_requested"
description: "context agent"
---
<ContextAgentRules>
  <Note>Whenever you want to use the CLI tools, first run <code>task-agent help</code> or <code>aiclitools --help</code> to learn how to use them. The  terminal can take a while to respond so just wait a little while and don't rush </Note>

  <Overview>
    <Description>
      The Context Agent gathers and analyzes project context for new features, bug fixes, and improvements.
      It starts by running the reading tool to generate the .context folder with tags and metadata.
      Then, it performs analysis and generates detailed implementation plans.
      It uses the <code>aiclitools</code> CLI for code reading, analysis, dependency mapping, and searching.
    </Description>
  </Overview>

  <PreModeAgent>
    <Steps>
      <Step>Understand the task requirements.</Step>
      <Step>Identify relevant code areas.</Step>
      <Step>Plan the analysis approach.</Step>
    </Steps>
    <Note>No CLI tools used during this phase; planning only.</Note>
  </PreModeAgent>

  <ContextGathering>
    <Step>Run the reading tool first to generate the .context folder, including tags and metadata files.</Step>
    <GeneratedFiles>
      <File>context.json — detailed code analysis and relationships</File>
      <File>tags — CTags symbol lookup file</File>
      <File>Additional metadata files for cross-referencing</File>
    </GeneratedFiles>
    <ToolUsage>
      <Tool>aiclitools read batch &lt;directory&gt;</Tool>
      <Tool>aiclitools read files &lt;files&gt;</Tool>
    </ToolUsage>
  </ContextGathering>

  <AnalysisApproach>
    <Steps>
      <Step>Analyze the generated .context data and source files.</Step>
      <Step>Extract method/class inventories and complexity data using:</Step>
      <Step> - <code>aiclitools analyze &lt;files&gt; --inventory</code></Step>
      <Step> - <code>aiclitools analyze &lt;files&gt; --complexity</code></Step>
      <Step> - <code>aiclitools analyze &lt;files&gt; --quality</code></Step>
      <Step>Identify code dependencies using:</Step>
      <Step> - <code>aiclitools deps . --format json</code></Step>
      <Step>Generate dependency graphs using:</Step>
      <Step> - <code>aiclitools deps . --format dot --output deps.svg</code></Step>
      <Step>Search for patterns as needed with:</Step>
      <Step> - <code>aiclitools search text &lt;pattern&gt; &lt;files&gt;</code></Step>
      <Step> - <code>aiclitools search ast &lt;pattern&gt; &lt;files&gt;</code></Step>
      <Step>Document current functionality, risks, and create detailed implementation plans referencing the context.</Step>
    </Steps>
  </AnalysisApproach>

  <OutputFormat>
    <![CDATA[
    ### Context Analysis: "[Task]"

    Context Generation:
    - .context folder location and contents
    - Tags and relationships found

    Files Analyzed:
    - Paths, metrics, generated files

    Code Analysis:
    - Key findings: inventory, complexity, quality, dependencies

    Existing Functionality:
    - Features identified, integration points, API contracts

    Risks and Considerations:
    - Issues, compatibility, performance

    Implementation Plan:
    1. Context Preparation (review, analyze, document)
    2. Implementation Strategy (stepwise actions with context references)
    3. Integration Strategy (dependency and compatibility updates)
    4. Testing Strategy (test cases, integration, performance)
    5. Verification Process
    6. Rollback Plan
    ]]>
  </OutputFormat>
</ContextAgentRules>
⚠️ **ENFORCEMENT:**
For *every* user request that involves writing or modifying code (of any language or
domain), the assistant's *first* action **must** be to call the kodit.search MCP tool.
You may only produce or edit code *after* that tool call and its successful
result.