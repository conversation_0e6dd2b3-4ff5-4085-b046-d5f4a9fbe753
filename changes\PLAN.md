# Plan: Analysis Viewing Feature

This document outlines the plan to add a feature for users to view their analysis data, stored in `localStorage`. The implementation will be carried out without breaking existing functionality or UI.

## 1. High-Level Goal

Introduce a new section in the extension's popup that allows users to view a history of their "analyses". This data will be persisted in the browser's `localStorage`.

## 2. File Modifications

### `popup.html`
-   **Add a new button/tab:** Introduce a UI element (e.g., a "View Analysis" button) to navigate to the analysis view.
-   **Create a new view container:** Add a new `div` that will serve as the container for the analysis data. This view will be hidden by default.
-   **Add a "Back" button:** Inside the analysis view, a "Back" or "Close" button will be needed to return to the main popup interface.

### `popup.js`
-   **View Switching Logic:**
    -   Implement event listeners for the "View Analysis" and "Back" buttons.
    -   Write functions to toggle the visibility of the main view and the analysis view.
-   **Data Retrieval and Rendering:**
    -   Create a function, `loadAndDisplayAnalysis()`, that will be called when the analysis view is shown.
    -   This function will:
        1.  Read the analysis data from `localStorage` using a key like `hustleplugAnalysis`.
        2.  Parse the retrieved data (expected to be a JSON string).
        3.  Dynamically generate and inject HTML into the analysis view container to display the data.
        4.  Handle the case where no analysis data is found, displaying a user-friendly message.
-   **(For testing) Dummy Data Generation:**
    -   To develop and test the viewing feature independently, a temporary function will be added to `popup.js` to populate `localStorage` with some sample analysis data.

### `content.css` or `styles/`
-   Add basic styling for the new analysis view to ensure it is visually consistent with the rest of the extension and is readable. This includes styles for the container, list items, and buttons.

## 3. Data Structure

The analysis data will be stored in `localStorage` as a JSON string. The parsed data will be an array of objects. Each object will represent a single analysis entry.

**Example Structure:**
```json
[
  {
    "id": "unique-id-1",
    "date": "2023-11-15T10:30:00Z",
    "summary": "This is the first analysis result."
  },
  {
    "id": "unique-id-2",
    "date": "2023-11-15T11:00:00Z",
    "summary": "This is the second analysis result."
  }
]
```

## 4. Implementation Steps

1.  **Phase 1: UI Scaffolding**
    -   Modify `popup.html` to add the necessary buttons and containers for the analysis view.
    -   Apply initial styles to hide the analysis view.

2.  **Phase 2: UI Logic**
    -   In `popup.js`, implement the JavaScript logic to switch between the main view and the analysis view.

3.  **Phase 3: Data Handling and Display**
    -   Implement the `loadAndDisplayAnalysis()` function in `popup.js`.
    -   Create the temporary function to add dummy data to `localStorage`.
    -   Add styling in `content.css` for the displayed analysis data.

4.  **Phase 4: Testing and Refinement**
    -   Use the dummy data function to populate `localStorage`.
    -   Thoroughly test the feature:
        -   Open the popup and switch to the analysis view.
        -   Verify data is displayed correctly.
        -   Verify the "no data" message appears when `localStorage` is empty.
        -   Ensure switching back to the main view works as expected.
        -   Confirm that no existing functionality is broken.

This phased approach ensures that we can build and test the feature incrementally without disrupting the current user experience.
