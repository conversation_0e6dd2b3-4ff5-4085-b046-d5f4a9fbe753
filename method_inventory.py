#!/usr/bin/env python3
"""
Method Inventory Generator for JavaScript
Creates complete catalog of all methods, classes, and their signatures
Prevents "method is not a function" errors during refactoring
"""

import re
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import defaultdict

class MethodInventory:
    def __init__(self):
        self.methods = []
        self.classes = []
        self.variables = []
        self.imports = []
        self.exports = []
    
    def extract_methods(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Extract all method definitions from JavaScript content"""
        methods = []
        lines = content.split('\n')
        
        # Different method patterns
        patterns = [
            # Regular functions
            (r'function\s+(\w+)\s*\(([^)]*)\)', 'function'),
            # Object methods
            (r'(\w+)\s*:\s*function\s*\(([^)]*)\)', 'object_method'),
            # ES6 class methods
            (r'^\s*(\w+)\s*\(([^)]*)\)\s*{', 'class_method'),
            # Arrow functions assigned to variables
            (r'(\w+)\s*=\s*\(([^)]*)\)\s*=>', 'arrow_function'),
            # Async functions
            (r'async\s+function\s+(\w+)\s*\(([^)]*)\)', 'async_function'),
            # Async methods
            (r'async\s+(\w+)\s*\(([^)]*)\)', 'async_method'),
        ]
        
        for i, line in enumerate(lines):
            stripped_line = line.strip()
            
            # Skip comments and empty lines
            if not stripped_line or stripped_line.startswith('//') or stripped_line.startswith('/*'):
                continue
            
            for pattern, method_type in patterns:
                match = re.search(pattern, stripped_line)
                if match:
                    method_name = match.group(1)
                    parameters = match.group(2) if len(match.groups()) > 1 else ''
                    
                    # Parse parameters
                    param_list = []
                    if parameters.strip():
                        param_list = [p.strip().split('=')[0].strip() for p in parameters.split(',')]
                    
                    # Determine scope (try to detect if it's inside a class)
                    scope = self.determine_scope(lines, i)
                    
                    methods.append({
                        'name': method_name,
                        'type': method_type,
                        'parameters': param_list,
                        'parameter_count': len(param_list),
                        'line': i + 1,
                        'scope': scope,
                        'signature': f"{method_name}({parameters})",
                        'file': file_path,
                        'is_async': 'async' in method_type,
                        'accessibility': self.determine_accessibility(stripped_line)
                    })
                    break
        
        return methods
    
    def determine_scope(self, lines: List[str], current_line: int) -> str:
        """Determine the scope of a method (global, class name, etc.)"""
        # Look backwards to find containing class or object
        for i in range(current_line - 1, -1, -1):
            line = lines[i].strip()
            
            # Check for class declaration
            class_match = re.search(r'class\s+(\w+)', line)
            if class_match:
                return class_match.group(1)
            
            # Check for object literal
            object_match = re.search(r'(\w+)\s*=\s*{', line)
            if object_match:
                return object_match.group(1)
            
            # Check for constructor or other class indicators
            if 'constructor' in line:
                # Find the class this constructor belongs to
                for j in range(i - 1, -1, -1):
                    class_line = lines[j].strip()
                    class_match = re.search(r'class\s+(\w+)', class_line)
                    if class_match:
                        return class_match.group(1)
        
        return 'global'
    
    def determine_accessibility(self, line: str) -> str:
        """Determine if method is public, private, or protected"""
        if line.startswith('_'):
            return 'private'
        elif 'private' in line:
            return 'private'
        elif 'protected' in line:
            return 'protected'
        else:
            return 'public'
    
    def extract_classes(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Extract all class definitions"""
        classes = []
        lines = content.split('\n')
        
        class_pattern = r'class\s+(\w+)(?:\s+extends\s+(\w+))?\s*{'
        
        for i, line in enumerate(lines):
            match = re.search(class_pattern, line.strip())
            if match:
                class_name = match.group(1)
                parent_class = match.group(2) if match.group(2) else None
                
                # Find class methods
                class_methods = []
                j = i + 1
                brace_count = 1
                
                while j < len(lines) and brace_count > 0:
                    current_line = lines[j]
                    brace_count += current_line.count('{') - current_line.count('}')
                    
                    # Look for method definitions within the class
                    method_match = re.search(r'^\s*(\w+)\s*\(([^)]*)\)', current_line.strip())
                    if method_match and not current_line.strip().startswith('//'):
                        method_name = method_match.group(1)
                        parameters = method_match.group(2)
                        class_methods.append({
                            'name': method_name,
                            'parameters': parameters,
                            'line': j + 1
                        })
                    
                    j += 1
                
                classes.append({
                    'name': class_name,
                    'parent': parent_class,
                    'line': i + 1,
                    'methods': class_methods,
                    'method_count': len(class_methods),
                    'file': file_path
                })
        
        return classes
    
    def extract_variables(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Extract variable declarations"""
        variables = []
        lines = content.split('\n')
        
        var_patterns = [
            r'var\s+(\w+)',
            r'let\s+(\w+)',
            r'const\s+(\w+)',
        ]
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('//'):
                continue
            
            for pattern in var_patterns:
                matches = re.findall(pattern, stripped)
                for var_name in matches:
                    variables.append({
                        'name': var_name,
                        'type': pattern.split('\\')[0],  # var, let, or const
                        'line': i + 1,
                        'file': file_path,
                        'scope': self.determine_scope(lines, i)
                    })
        
        return variables
    
    def extract_imports_exports(self, content: str, file_path: str) -> tuple:
        """Extract import and export statements"""
        imports = []
        exports = []
        
        # Import patterns
        import_patterns = [
            r'import\s+(\w+)\s+from\s+[\'"]([^\'"]+)[\'"]',  # import name from 'module'
            r'import\s+{\s*([^}]+)\s*}\s+from\s+[\'"]([^\'"]+)[\'"]',  # import { a, b } from 'module'
            r'import\s+\*\s+as\s+(\w+)\s+from\s+[\'"]([^\'"]+)[\'"]',  # import * as name from 'module'
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if len(match) == 2:
                    if '{' in match[0]:  # Named imports
                        names = [name.strip() for name in match[0].split(',')]
                        for name in names:
                            imports.append({
                                'name': name,
                                'module': match[1],
                                'type': 'named',
                                'file': file_path
                            })
                    else:  # Default or namespace import
                        imports.append({
                            'name': match[0],
                            'module': match[1],
                            'type': 'default' if '*' not in pattern else 'namespace',
                            'file': file_path
                        })
        
        # Export patterns
        export_patterns = [
            r'export\s+function\s+(\w+)',
            r'export\s+class\s+(\w+)',
            r'export\s+const\s+(\w+)',
            r'export\s+let\s+(\w+)',
            r'export\s+var\s+(\w+)',
            r'export\s+{\s*([^}]+)\s*}',
            r'export\s+default\s+(\w+)',
        ]
        
        for pattern in export_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if '{' in pattern:  # Named exports list
                    names = [name.strip() for name in match.split(',')]
                    for name in names:
                        exports.append({
                            'name': name,
                            'type': 'named',
                            'file': file_path
                        })
                else:
                    export_type = 'default' if 'default' in pattern else 'named'
                    exports.append({
                        'name': match,
                        'type': export_type,
                        'file': file_path
                    })
        
        return imports, exports
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a JavaScript file and create complete inventory"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f"Could not read file: {e}"}
        
        # Extract all components
        methods = self.extract_methods(content, file_path)
        classes = self.extract_classes(content, file_path)
        variables = self.extract_variables(content, file_path)
        imports, exports = self.extract_imports_exports(content, file_path)
        
        # Calculate statistics
        stats = {
            'total_methods': len(methods),
            'total_classes': len(classes),
            'total_variables': len(variables),
            'total_imports': len(imports),
            'total_exports': len(exports),
            'lines_of_code': len(content.split('\n')),
        }
        
        # Group methods by scope
        methods_by_scope = defaultdict(list)
        for method in methods:
            methods_by_scope[method['scope']].append(method)
        
        # Identify potential issues
        issues = []
        
        # Check for duplicate method names
        method_names = [m['name'] for m in methods]
        duplicates = [name for name in set(method_names) if method_names.count(name) > 1]
        if duplicates:
            issues.append({
                'type': 'DUPLICATE_METHODS',
                'message': f'Duplicate method names found: {", ".join(duplicates)}',
                'severity': 'MEDIUM'
            })
        
        # Check for unused imports
        imported_names = [imp['name'] for imp in imports]
        used_names = set()
        for method in methods:
            for param in method['parameters']:
                used_names.add(param)
        
        potentially_unused = [name for name in imported_names if name not in content]
        if potentially_unused:
            issues.append({
                'type': 'POTENTIALLY_UNUSED_IMPORTS',
                'message': f'Potentially unused imports: {", ".join(potentially_unused)}',
                'severity': 'LOW'
            })
        
        return {
            'file': file_path,
            'statistics': stats,
            'methods': methods,
            'classes': classes,
            'variables': variables,
            'imports': imports,
            'exports': exports,
            'methods_by_scope': dict(methods_by_scope),
            'issues': issues,
            'api_surface': {
                'public_methods': [m for m in methods if m['accessibility'] == 'public'],
                'exported_items': exports,
                'class_methods': {cls['name']: cls['methods'] for cls in classes}
            }
        }
    
    def compare_inventories(self, before: Dict[str, Any], after: Dict[str, Any]) -> Dict[str, Any]:
        """Compare two inventories to find differences"""
        if 'error' in before or 'error' in after:
            return {'error': 'Cannot compare inventories with errors'}
        
        before_methods = {m['name']: m for m in before['methods']}
        after_methods = {m['name']: m for m in after['methods']}
        
        missing_methods = set(before_methods.keys()) - set(after_methods.keys())
        new_methods = set(after_methods.keys()) - set(before_methods.keys())
        
        # Check for signature changes
        signature_changes = []
        for name in set(before_methods.keys()) & set(after_methods.keys()):
            before_sig = before_methods[name]['signature']
            after_sig = after_methods[name]['signature']
            if before_sig != after_sig:
                signature_changes.append({
                    'method': name,
                    'before': before_sig,
                    'after': after_sig
                })
        
        return {
            'missing_methods': list(missing_methods),
            'new_methods': list(new_methods),
            'signature_changes': signature_changes,
            'statistics_change': {
                'methods': after['statistics']['total_methods'] - before['statistics']['total_methods'],
                'classes': after['statistics']['total_classes'] - before['statistics']['total_classes'],
                'lines': after['statistics']['lines_of_code'] - before['statistics']['lines_of_code']
            }
        }
    
    def generate_report(self, inventory: Dict[str, Any], output_file: str = None) -> str:
        """Generate comprehensive inventory report"""
        if 'error' in inventory:
            return f"Error: {inventory['error']}"
        
        report = []
        report.append("📋 Method Inventory Report")
        report.append("=" * 50)
        report.append("")
        
        # Statistics
        stats = inventory['statistics']
        report.append(f"📊 File: {inventory['file']}")
        report.append(f"  • Lines of code: {stats['lines_of_code']}")
        report.append(f"  • Methods: {stats['total_methods']}")
        report.append(f"  • Classes: {stats['total_classes']}")
        report.append(f"  • Variables: {stats['total_variables']}")
        report.append(f"  • Imports: {stats['total_imports']}")
        report.append(f"  • Exports: {stats['total_exports']}")
        report.append("")
        
        # Methods by scope
        if inventory['methods_by_scope']:
            report.append("🎯 Methods by Scope:")
            report.append("-" * 25)
            for scope, methods in inventory['methods_by_scope'].items():
                report.append(f"📁 {scope}: {len(methods)} methods")
                for method in methods[:5]:  # Show first 5
                    async_marker = "async " if method['is_async'] else ""
                    report.append(f"   • {async_marker}{method['signature']}")
                if len(methods) > 5:
                    report.append(f"   ... and {len(methods) - 5} more")
                report.append("")
        
        # Classes
        if inventory['classes']:
            report.append("🏗️  Classes:")
            report.append("-" * 15)
            for cls in inventory['classes']:
                parent_info = f" extends {cls['parent']}" if cls['parent'] else ""
                report.append(f"📦 {cls['name']}{parent_info} ({cls['method_count']} methods)")
                for method in cls['methods'][:3]:  # Show first 3 methods
                    report.append(f"   • {method['name']}({method['parameters']})")
                if cls['method_count'] > 3:
                    report.append(f"   ... and {cls['method_count'] - 3} more methods")
                report.append("")
        
        # API Surface
        api = inventory['api_surface']
        if api['public_methods'] or api['exported_items']:
            report.append("🔌 Public API Surface:")
            report.append("-" * 25)
            
            if api['exported_items']:
                report.append("📤 Exports:")
                for export in api['exported_items']:
                    export_type = f"({export['type']})" if export['type'] != 'named' else ""
                    report.append(f"   • {export['name']} {export_type}")
                report.append("")
            
            if api['public_methods']:
                report.append("🔓 Public Methods:")
                for method in api['public_methods'][:10]:  # Show first 10
                    report.append(f"   • {method['signature']}")
                if len(api['public_methods']) > 10:
                    report.append(f"   ... and {len(api['public_methods']) - 10} more")
                report.append("")
        
        # Issues
        if inventory['issues']:
            report.append("⚠️  Potential Issues:")
            report.append("-" * 20)
            for issue in inventory['issues']:
                severity_emoji = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}.get(issue['severity'], '⚪')
                report.append(f"{severity_emoji} {issue['type']}: {issue['message']}")
            report.append("")
        
        # Refactoring guidance
        report.append("🎯 Refactoring Guidance:")
        report.append("-" * 25)
        
        method_count = stats['total_methods']
        if method_count > 50:
            report.append("🚨 High method count - consider splitting into modules")
        elif method_count > 20:
            report.append("⚠️  Moderate method count - good candidate for modularization")
        else:
            report.append("✅ Method count is manageable")
        
        if stats['total_classes'] > 5:
            report.append("📦 Multiple classes - consider separate files")
        
        if stats['lines_of_code'] > 500:
            report.append("📏 Large file - priority candidate for refactoring")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 Report saved to: {output_file}")
        
        return report_text

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 method_inventory.py <javascript-file> [output-file]")
        print("Example: python3 method_inventory.py popup.js inventory-report.txt")
        sys.exit(1)
    
    file_path = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(file_path).exists():
        print(f"❌ Error: File '{file_path}' not found")
        sys.exit(1)
    
    print(f"📋 Creating method inventory for {file_path}...")
    
    inventory_tool = MethodInventory()
    inventory = inventory_tool.analyze_file(file_path)
    
    if 'error' in inventory:
        print(f"❌ {inventory['error']}")
        sys.exit(1)
    
    # Generate and display report
    report = inventory_tool.generate_report(inventory, output_file)
    print(report)
    
    # Save JSON inventory
    json_file = f"{Path(file_path).stem}-inventory.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(inventory, f, indent=2)
    print(f"📊 JSON inventory saved to: {json_file}")
    
    # Summary
    stats = inventory['statistics']
    print(f"\n🎯 INVENTORY COMPLETE:")
    print(f"   • {stats['total_methods']} methods cataloged")
    print(f"   • {stats['total_classes']} classes found")
    print(f"   • {len(inventory['api_surface']['exported_items'])} items exported")
    print(f"   • Ready for safe refactoring!")

if __name__ == "__main__":
    main() 