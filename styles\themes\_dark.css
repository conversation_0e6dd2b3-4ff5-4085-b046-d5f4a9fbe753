/* API Status */
.api-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #1D1A2A;
    border-radius: 10px;
    border: 1px solid #2C2738;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF5C5C;
    animation: blink 2s infinite;
}

.status-indicator.connected {
    background: #28a745;
    animation: none;
}

.status-text {
    color: #B2AFC5;
    font-size: 14px;
}

.api-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #1D1A2A;
    border-radius: 10px;
    border: 1px solid #2C2738;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF5C5C;
    animation: blink 2s infinite;
}

.status-indicator.connected {
    background: #28a745;
    animation: none;
}

.status-text {
    color: #B2AFC5;
    font-size: 14px;
}
