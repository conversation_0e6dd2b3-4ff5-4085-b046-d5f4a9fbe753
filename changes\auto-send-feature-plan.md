# Plan: Auto-Send Feature for Discord & Telegram Integrations

This document outlines the plan to implement an auto-send feature that automatically sends analysis results to configured integrations (Discord and/or Telegram) with user-controlled toggle settings.

## 1. High-Level Goal

Add an auto-send feature that:
- Automatically sends analysis results to configured Discord/Telegram integrations
- Provides individual toggle controls for each integration in settings
- Maintains backwards compatibility with existing manual send functionality
- Respects Pro feature gating (both integrations are Pro-only)
- Provides user feedback and error handling

## 2. Feature Requirements

### Core Functionality
- **Auto-send toggles:** Individual on/off switches for Discord and Telegram auto-send
- **Integration with analysis flow:** Automatically trigger sends after successful analysis
- **Error handling:** Graceful failure with user notifications
- **Settings persistence:** Save auto-send preferences in browser storage
- **Pro gating:** Feature only available to Pro users

### User Experience
- **Clear UI controls:** Toggle switches in integration settings sections
- **Status indicators:** Show when auto-send is enabled/disabled
- **Feedback messages:** Success/failure notifications for auto-sends
- **Manual override:** Manual send buttons remain functional alongside auto-send

## 3. File Modifications

### `popup.html`
- **Discord Settings Section:** Add auto-send toggle switch in Discord settings
- **Telegram Settings Section:** Add auto-send toggle switch in Telegram settings
- **Status indicators:** Add visual indicators for auto-send status

### `popup.js`
- **Settings Management:**
  - Add auto-send toggle event listeners
  - Implement `saveAutoSendSettings()` and `loadAutoSendSettings()` functions
  - Update settings loading/saving logic to include auto-send preferences
- **Analysis Integration:**
  - Modify analysis completion flow to trigger auto-sends
  - Add `handleAutoSend()` function to orchestrate automatic sending
  - Implement error handling and user feedback for auto-sends
- **UI Updates:**
  - Update settings display to show auto-send status
  - Add toggle switch styling and behavior

### `js/integrations/discord.js`
- **Auto-send Support:** Ensure `sendToDiscord()` function is compatible with auto-send calls
- **Error Handling:** Return proper success/failure status for auto-send feedback

### `js/integrations/telegram.js`
- **Auto-send Support:** Ensure `sendToTelegram()` function is compatible with auto-send calls
- **Error Handling:** Return proper success/failure status for auto-send feedback

### `js/user/discordSettings.js`
- **Settings Extension:** Add auto-send preference management
- **Validation:** Ensure auto-send can only be enabled with valid webhook URL

### `js/user/telegramSettings.js`
- **Settings Extension:** Add auto-send preference management
- **Validation:** Ensure auto-send can only be enabled with valid bot token and chat ID

### `styles/popup.css`
- **Toggle Switches:** Add styling for auto-send toggle switches
- **Status Indicators:** Style for enabled/disabled status indicators
- **Integration with existing design:** Maintain consistent UI/UX

### `config.js`
- **Storage Keys:** Add constants for auto-send settings storage keys
- **Default Values:** Define default auto-send settings

## 4. Data Structure

### Auto-Send Settings Storage
```javascript
// Stored in browser storage
const autoSendSettings = {
  discord: {
    enabled: false,
    lastSent: null, // timestamp of last auto-send
    failureCount: 0 // track consecutive failures
  },
  telegram: {
    enabled: false,
    lastSent: null,
    failureCount: 0
  }
};
```

### Integration Status Tracking
```javascript
// Runtime status tracking
const autoSendStatus = {
  discord: {
    isConfigured: false, // has valid webhook URL
    canAutoSend: false,  // configured + Pro + enabled
    lastError: null
  },
  telegram: {
    isConfigured: false, // has valid bot token + chat ID
    canAutoSend: false,  // configured + Pro + enabled
    lastError: null
  }
};
```

## 5. Implementation Steps

### Phase 1: Settings Infrastructure
1. **Storage Management:**
   - Add auto-send settings constants to `config.js`
   - Implement settings save/load functions in relevant settings files
   - Add browser storage handling for auto-send preferences

2. **UI Components:**
   - Add toggle switches to Discord and Telegram settings sections in `popup.html`
   - Implement toggle switch styling in `popup.css`
   - Add status indicators for auto-send state

### Phase 2: Settings Logic
1. **Toggle Functionality:**
   - Add event listeners for auto-send toggles in `popup.js`
   - Implement toggle state management and persistence
   - Add validation (Pro status, integration configuration)

2. **Settings Integration:**
   - Update Discord and Telegram settings modules to handle auto-send preferences
   - Ensure auto-send can only be enabled when integrations are properly configured
   - Add settings validation and error handling

### Phase 3: Auto-Send Core Logic
1. **Analysis Integration:**
   - Identify analysis completion points in existing code
   - Implement `handleAutoSend()` function to orchestrate automatic sending
   - Add conditional logic to trigger auto-sends based on settings

2. **Send Orchestration:**
   - Create unified auto-send handler that manages both Discord and Telegram
   - Implement parallel sending when both integrations are enabled
   - Add proper error handling and retry logic

### Phase 4: User Feedback & Error Handling
1. **Status Feedback:**
   - Add success/failure notifications for auto-sends
   - Implement status indicators in UI
   - Add error message display and handling

2. **Failure Management:**
   - Implement failure counting and temporary disable logic
   - Add user notifications for repeated failures
   - Provide manual re-enable options after failures

### Phase 5: Testing & Refinement
1. **Functionality Testing:**
   - Test auto-send with Discord only, Telegram only, and both enabled
   - Verify Pro gating works correctly
   - Test error scenarios (invalid webhooks, network failures)

2. **UI/UX Testing:**
   - Verify toggle switches work correctly
   - Test status indicators and feedback messages
   - Ensure backwards compatibility with manual send functionality

3. **Edge Case Testing:**
   - Test with missing/invalid integration configurations
   - Test rapid analysis scenarios
   - Verify settings persistence across browser sessions

## 6. Technical Considerations

### Performance
- **Async Operations:** Use Promise-based approach for parallel Discord/Telegram sends
- **Rate Limiting:** Respect Discord/Telegram API rate limits
- **Error Recovery:** Implement exponential backoff for failed sends

### Security
- **Settings Validation:** Validate all auto-send settings before enabling
- **Error Logging:** Log errors without exposing sensitive data
- **Pro Verification:** Ensure Pro status is verified before enabling auto-send

### Backwards Compatibility
- **Existing Functionality:** All current manual send features remain unchanged
- **Settings Migration:** Handle users upgrading from version without auto-send
- **Graceful Degradation:** Auto-send failures don't break manual functionality

## 7. Success Criteria

- ✅ Users can enable/disable auto-send for Discord and Telegram independently
- ✅ Analysis results automatically send to configured integrations when enabled
- ✅ Pro gating prevents non-Pro users from accessing auto-send features
- ✅ Clear user feedback for auto-send success/failure states
- ✅ Manual send functionality remains fully operational
- ✅ Settings persist across browser sessions
- ✅ Graceful error handling for network/API failures
- ✅ UI maintains consistent design with existing integration settings

## 8. Future Enhancements (Out of Scope)

- **Send Scheduling:** Delayed or scheduled auto-sends
- **Conditional Sending:** Auto-send based on analysis results/criteria
- **Send History:** Detailed log of auto-send attempts and results
- **Bulk Management:** Enable/disable all auto-sends with single toggle
- **Custom Templates:** Different message formats for auto-send vs manual send 