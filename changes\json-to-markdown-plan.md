### Plan to Change Downloaded Analysis File from JSON to Markdown

**Objective**: Modify the extension to download analysis files in Markdown format instead of JSON, without disrupting existing functionality or UI.

#### 1. **Understand the Existing Code**

*   **File of Interest**: `popup.js`
*   **Key Functionality**: The download functionality is located in `popup.js`.
*   **Current Implementation**:
    *   The code stringifies an `exportData` object into a JSON string.
    *   A `Blob` is created with `type: 'application/json'`.
    *   The download link's `download` attribute is set to a filename ending in `.json`.

#### 2. **Create a Markdown Conversion Function**

*   **New Function**: Create a new function, let's call it `convertToMarkdown`, that takes the `exportData` object as input.
*   **Function Logic**: This function will be responsible for converting the JSON object into a well-formatted Markdown string. The structure of the Markdown file should be considered (e.g., using headers, lists, code blocks).
*   **Location**: This new function can be added to `popup.js` or a new utility file if we want to keep the code modular.

#### 3. **Modify the Download Logic**

*   **In `popup.js`**:
    *   Instead of `JSON.stringify`, call the new `convertToMarkdown` function to get the file content.
    *   Change the `Blob` type from `application/json` to `text/markdown`.
    *   Update the `download` attribute to use a `.md` extension, for example: `agent-hustle-analysis-${Date.now()}.md`.

#### 4. **Testing**

*   **Manual Testing**:
    *   Perform an analysis in the extension.
    *   Click the download button.
    *   Verify that the downloaded file is in Markdown format (`.md`).
    *   Check the content of the downloaded file to ensure it is well-formatted and contains the correct analysis data.
*   **No Automated Tests**: Since there are no automated tests for this part of the application, thorough manual testing is crucial.

#### 5. **Code Changes Summary**

*   **`popup.js`**:
    *   Add `convertToMarkdown` function.
    *   Modify the download button's event listener to use the new function and update the Blob type and filename.

By following this plan, we can successfully change the download format to Markdown while ensuring the rest of the extension remains functional. 