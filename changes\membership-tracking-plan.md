# Plan: Membership Tracking & Automatic Key Expiration Management

This document outlines the plan to implement comprehensive membership tracking, automatic key expiration, and user lifecycle management for the Agent Hustle Pro system.

## 1. High-Level Goal

Implement a robust membership management system that:
- Tracks membership start/end dates for each Pro key
- Automatically handles key expiration and transitions back to regular status
- Provides membership analytics and user lifecycle insights
- Maintains audit trails for membership changes
- Supports different membership tiers and durations

## 2. Current System Analysis

### **Existing Pro Key Structure:**
```json
{
  "pro-keys-hashed.json": [
    "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
    "f6e5d4c3b2a1098765432109876543210987654321fedcba0987654321fedcba"
  ]
}
```

### **Current Limitations:**
- No membership duration tracking
- No expiration dates
- No user identification
- No membership analytics
- Manual key management only

## 3. Enhanced Membership System Design

### **3.1 New JSON Structure**

#### **Enhanced Pro Keys File: `pro-keys-enhanced.json`**
```json
{
  "version": "2.0",
  "lastUpdated": "2025-06-13T16:52:04.000Z",
  "keys": {
    "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456": {
      "keyHash": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
      "userId": "user_12345",
      "email": "<EMAIL>",
      "membershipTier": "pro",
      "status": "active",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "activatedAt": "2025-01-01T12:00:00.000Z",
      "expiresAt": "2026-01-01T00:00:00.000Z",
      "lastUsed": "2025-06-13T16:52:04.000Z",
      "usageCount": 1247,
      "autoRenew": false,
      "metadata": {
        "source": "stripe_payment",
        "paymentId": "pi_1234567890",
        "planId": "pro_annual",
        "notes": "Annual subscription"
      }
    },
    "f6e5d4c3b2a1098765432109876543210987654321fedcba0987654321fedcba": {
      "keyHash": "f6e5d4c3b2a1098765432109876543210987654321fedcba0987654321fedcba",
      "userId": "user_67890",
      "email": "<EMAIL>",
      "membershipTier": "premium",
      "status": "expired",
      "createdAt": "2024-06-01T00:00:00.000Z",
      "activatedAt": "2024-06-01T09:30:00.000Z",
      "expiresAt": "2025-06-01T00:00:00.000Z",
      "expiredAt": "2025-06-01T00:00:00.000Z",
      "lastUsed": "2025-05-30T14:22:15.000Z",
      "usageCount": 892,
      "autoRenew": false,
      "metadata": {
        "source": "manual_grant",
        "grantedBy": "admin_user",
        "planId": "premium_monthly",
        "notes": "Beta tester access"
      }
    }
  },
  "statistics": {
    "totalKeys": 2,
    "activeKeys": 1,
    "expiredKeys": 1,
    "totalUsage": 2139,
    "averageUsagePerKey": 1069.5
  }
}
```

#### **Membership Analytics File: `membership-analytics.json`**
```json
{
  "version": "1.0",
  "lastUpdated": "2025-06-13T16:52:04.000Z",
  "analytics": {
    "daily": {
      "2025-06-13": {
        "activeMembers": 1,
        "newSignups": 0,
        "expirations": 0,
        "totalUsage": 15,
        "revenue": 0
      }
    },
    "monthly": {
      "2025-06": {
        "activeMembers": 1,
        "newSignups": 0,
        "expirations": 1,
        "totalUsage": 450,
        "revenue": 0,
        "churnRate": 0.5
      }
    },
    "membershipTiers": {
      "pro": {
        "active": 1,
        "expired": 0,
        "totalRevenue": 0
      },
      "premium": {
        "active": 0,
        "expired": 1,
        "totalRevenue": 0
      }
    }
  }
}
```

### **3.2 Membership Status Types**

```javascript
const MEMBERSHIP_STATUS = {
  ACTIVE: 'active',           // Valid and within expiration date
  EXPIRED: 'expired',         // Past expiration date
  SUSPENDED: 'suspended',     // Temporarily disabled
  CANCELLED: 'cancelled',     // User cancelled, grace period
  PENDING: 'pending',         // Payment pending
  TRIAL: 'trial'             // Trial period
};

const MEMBERSHIP_TIERS = {
  BASIC: 'basic',            // Free tier
  PRO: 'pro',                // Standard Pro features
  PREMIUM: 'premium',        // Enhanced Pro features
  ENTERPRISE: 'enterprise'   // Full feature set
};
```

## 4. Implementation Plan

### **Phase 1: Enhanced Pro Status Module**

#### **4.1 Update `js/user/proStatus.js`**

**New Functions to Add:**
```javascript
/**
 * Enhanced pro status check with membership details
 * @param {string} proKey - The pro key to validate
 * @returns {Promise<Object>} - Detailed membership status
 */
export async function getDetailedProStatus(proKey) {
  // Returns comprehensive membership information
}

/**
 * Check if membership is expired and handle transition
 * @param {string} proKey - The pro key to check
 * @returns {Promise<Object>} - Expiration status and actions taken
 */
export async function checkMembershipExpiration(proKey) {
  // Handles automatic expiration and status updates
}

/**
 * Get membership analytics for current user
 * @param {string} proKey - The pro key
 * @returns {Promise<Object>} - User-specific analytics
 */
export async function getMembershipAnalytics(proKey) {
  // Returns usage stats, remaining time, etc.
}

/**
 * Update membership usage tracking
 * @param {string} proKey - The pro key
 * @param {string} feature - Feature used
 * @returns {Promise<boolean>} - Success status
 */
export async function trackFeatureUsage(proKey, feature) {
  // Tracks feature usage for analytics
}
```

#### **4.2 Enhanced Validation Logic**
```javascript
export async function checkProStatus() {
  try {
    const proKey = await getStoredProKey();
    if (!proKey) {
      return { isPro: false, status: 'no_key' };
    }

    // Get detailed membership status
    const membershipStatus = await getDetailedProStatus(proKey);
    
    // Check for expiration
    const expirationCheck = await checkMembershipExpiration(proKey);
    
    if (expirationCheck.isExpired) {
      // Handle automatic transition to regular user
      await handleMembershipExpiration(proKey);
      return { 
        isPro: false, 
        status: 'expired',
        expiredAt: expirationCheck.expiredAt,
        wasProUser: true
      };
    }

    // Track this status check
    await trackFeatureUsage(proKey, 'status_check');

    return {
      isPro: true,
      status: membershipStatus.status,
      tier: membershipStatus.membershipTier,
      expiresAt: membershipStatus.expiresAt,
      daysRemaining: membershipStatus.daysRemaining,
      usageCount: membershipStatus.usageCount
    };

  } catch (error) {
    console.error('Error checking pro status:', error);
    return { isPro: false, status: 'error', error: error.message };
  }
}
```

### **Phase 2: Membership Management Module**

#### **4.3 New File: `js/user/membershipManager.js`**

```javascript
/**
 * Comprehensive membership management system
 */
export class MembershipManager {
  constructor() {
    this.apiEndpoint = 'https://calel33.github.io/json-validate/pro-keys-enhanced.json';
    this.analyticsEndpoint = 'https://calel33.github.io/json-validate/membership-analytics.json';
  }

  /**
   * Get comprehensive membership data
   */
  async getMembershipData(proKey) {
    // Fetch and validate membership information
  }

  /**
   * Handle membership expiration
   */
  async handleExpiration(proKey) {
    // Automatic transition logic
  }

  /**
   * Track membership analytics
   */
  async trackAnalytics(proKey, event, data) {
    // Analytics tracking
  }

  /**
   * Get membership insights for user
   */
  async getMembershipInsights(proKey) {
    // User-facing analytics
  }

  /**
   * Check for membership renewal opportunities
   */
  async checkRenewalEligibility(proKey) {
    // Renewal logic
  }
}
```

### **Phase 3: User Interface Enhancements**

#### **4.4 Enhanced Pro Status Display**

**Update `popup.js` to show membership details:**
```javascript
async function updateProKeyStatus(status, message) {
  const statusElement = document.getElementById('proKeyStatus');
  const indicator = statusElement.querySelector('.status-indicator');
  const text = statusElement.querySelector('.status-text');
  
  if (status.isPro) {
    indicator.classList.add('valid');
    indicator.classList.remove('invalid', 'warning');
    
    // Enhanced status message with membership details
    const daysRemaining = Math.ceil((new Date(status.expiresAt) - new Date()) / (1000 * 60 * 60 * 24));
    
    if (daysRemaining <= 7) {
      indicator.classList.add('warning');
      text.innerHTML = `
        <div class="membership-status">
          <div class="status-main">Pro Active - ${status.tier.toUpperCase()}</div>
          <div class="status-warning">⚠️ Expires in ${daysRemaining} days</div>
          <div class="status-details">Used ${status.usageCount} times</div>
        </div>
      `;
    } else {
      text.innerHTML = `
        <div class="membership-status">
          <div class="status-main">Pro Active - ${status.tier.toUpperCase()}</div>
          <div class="status-details">Expires: ${new Date(status.expiresAt).toLocaleDateString()}</div>
          <div class="status-details">Used ${status.usageCount} times</div>
        </div>
      `;
    }
  } else if (status.wasProUser) {
    indicator.classList.add('invalid');
    text.innerHTML = `
      <div class="membership-status">
        <div class="status-main">Membership Expired</div>
        <div class="status-details">Expired: ${new Date(status.expiredAt).toLocaleDateString()}</div>
        <div class="status-action">
          <a href="https://agenthustle.ai/renew" target="_blank">Renew Membership →</a>
        </div>
      </div>
    `;
  }
}
```

#### **4.5 Membership Dashboard Section**

**New section in `popup.html`:**
```html
<!-- Membership Dashboard -->
<div id="membershipDashboard" class="section" style="display: none;">
  <div class="section-header">
    <h3>📊 Membership Dashboard</h3>
    <button id="backToActionsFromDashboard" class="btn btn-secondary btn-sm">← Back</button>
  </div>
  
  <div class="membership-overview">
    <div class="membership-card">
      <div class="membership-tier">
        <span class="tier-badge" id="membershipTier">PRO</span>
        <span class="tier-status" id="membershipStatus">Active</span>
      </div>
      
      <div class="membership-details">
        <div class="detail-item">
          <label>Member Since:</label>
          <span id="memberSince">January 1, 2025</span>
        </div>
        <div class="detail-item">
          <label>Expires:</label>
          <span id="expiresOn">January 1, 2026</span>
        </div>
        <div class="detail-item">
          <label>Days Remaining:</label>
          <span id="daysRemaining">201 days</span>
        </div>
        <div class="detail-item">
          <label>Total Usage:</label>
          <span id="totalUsage">1,247 analyses</span>
        </div>
      </div>
    </div>
    
    <div class="usage-analytics">
      <h4>📈 Usage Analytics</h4>
      <div class="analytics-grid">
        <div class="analytics-item">
          <div class="analytics-value" id="thisMonthUsage">45</div>
          <div class="analytics-label">This Month</div>
        </div>
        <div class="analytics-item">
          <div class="analytics-value" id="avgDailyUsage">1.5</div>
          <div class="analytics-label">Daily Average</div>
        </div>
        <div class="analytics-item">
          <div class="analytics-value" id="favoriteFeature">Custom Analysis</div>
          <div class="analytics-label">Most Used</div>
        </div>
      </div>
    </div>
    
    <div class="membership-actions">
      <button id="renewMembership" class="btn btn-primary">🔄 Renew Membership</button>
      <button id="downloadUsageReport" class="btn btn-outline">📊 Download Report</button>
    </div>
  </div>
</div>
```

### **Phase 4: Automatic Expiration Handling**

#### **4.6 Background Expiration Checker**

**New file: `js/utils/expirationChecker.js`**
```javascript
/**
 * Background service for checking membership expiration
 */
export class ExpirationChecker {
  constructor() {
    this.checkInterval = 24 * 60 * 60 * 1000; // 24 hours
    this.warningThresholds = [30, 7, 3, 1]; // Days before expiration
  }

  /**
   * Start periodic expiration checking
   */
  startPeriodicCheck() {
    setInterval(async () => {
      await this.checkAllMemberships();
    }, this.checkInterval);
  }

  /**
   * Check all stored memberships for expiration
   */
  async checkAllMemberships() {
    try {
      const proKey = await getStoredProKey();
      if (proKey) {
        await this.checkMembershipExpiration(proKey);
      }
    } catch (error) {
      console.error('Error in periodic expiration check:', error);
    }
  }

  /**
   * Handle membership expiration with user notification
   */
  async handleExpiration(membershipData) {
    // Show expiration notification
    // Update local storage
    // Trigger UI updates
    // Log expiration event
  }

  /**
   * Send expiration warnings
   */
  async sendExpirationWarning(membershipData, daysRemaining) {
    // Show warning notification
    // Update UI with warning state
    // Log warning event
  }
}
```

### **Phase 5: Analytics and Reporting**

#### **4.7 Usage Analytics Module**

**New file: `js/analytics/usageAnalytics.js`**
```javascript
/**
 * Comprehensive usage analytics system
 */
export class UsageAnalytics {
  constructor() {
    this.events = [];
    this.sessionStart = Date.now();
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(feature, metadata = {}) {
    const event = {
      timestamp: new Date().toISOString(),
      feature: feature,
      sessionId: this.sessionStart,
      metadata: metadata
    };
    
    this.events.push(event);
    await this.persistEvent(event);
  }

  /**
   * Generate usage report
   */
  async generateUsageReport(proKey, timeframe = '30d') {
    // Generate comprehensive usage report
  }

  /**
   * Get usage insights
   */
  async getUsageInsights(proKey) {
    // Return user-friendly insights
  }
}
```

## 5. Data Migration Strategy

### **5.1 Backward Compatibility**

**Migration from current system:**
```javascript
/**
 * Migrate from simple array to enhanced object structure
 */
async function migrateProKeysData() {
  try {
    // Fetch current simple array
    const response = await fetch('https://calel33.github.io/json-validate/pro-keys-hashed.json');
    const currentKeys = await response.json();
    
    // Convert to enhanced structure
    const enhancedData = {
      version: "2.0",
      lastUpdated: new Date().toISOString(),
      keys: {},
      statistics: {
        totalKeys: currentKeys.length,
        activeKeys: currentKeys.length,
        expiredKeys: 0,
        totalUsage: 0,
        averageUsagePerKey: 0
      }
    };
    
    // Migrate each key with default values
    currentKeys.forEach((keyHash, index) => {
      enhancedData.keys[keyHash] = {
        keyHash: keyHash,
        userId: `migrated_user_${index + 1}`,
        email: null,
        membershipTier: "pro",
        status: "active",
        createdAt: "2025-01-01T00:00:00.000Z", // Default creation date
        activatedAt: "2025-01-01T00:00:00.000Z",
        expiresAt: "2026-01-01T00:00:00.000Z", // 1 year from migration
        lastUsed: null,
        usageCount: 0,
        autoRenew: false,
        metadata: {
          source: "migration",
          notes: "Migrated from simple array format"
        }
      };
    });
    
    return enhancedData;
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}
```

### **5.2 Gradual Rollout**

**Feature flag approach:**
```javascript
const FEATURE_FLAGS = {
  ENHANCED_MEMBERSHIP: true,
  USAGE_ANALYTICS: true,
  EXPIRATION_WARNINGS: true,
  MEMBERSHIP_DASHBOARD: false // Gradual rollout
};
```

## 6. Security Considerations

### **6.1 Data Protection**
- Hash sensitive user information
- Encrypt membership data in transit
- Implement rate limiting for API calls
- Audit trail for all membership changes

### **6.2 Privacy Compliance**
- Minimal data collection
- User consent for analytics
- Data retention policies
- GDPR compliance considerations

## 7. Implementation Timeline

### **Week 1: Core Infrastructure**
- [ ] Enhanced pro status module
- [ ] Membership manager class
- [ ] Data structure design
- [ ] Migration utilities

### **Week 2: Expiration Handling**
- [ ] Automatic expiration checker
- [ ] User notification system
- [ ] Status transition logic
- [ ] Testing framework

### **Week 3: Analytics & UI**
- [ ] Usage analytics module
- [ ] Membership dashboard
- [ ] Enhanced status displays
- [ ] Reporting features

### **Week 4: Testing & Deployment**
- [ ] Comprehensive testing
- [ ] Migration testing
- [ ] Performance optimization
- [ ] Production deployment

## 8. Success Metrics

### **8.1 Technical Metrics**
- ✅ Zero data loss during migration
- ✅ 100% backward compatibility
- ✅ <100ms response time for status checks
- ✅ 99.9% uptime for membership services

### **8.2 User Experience Metrics**
- ✅ Clear membership status visibility
- ✅ Proactive expiration warnings
- ✅ Seamless renewal process
- ✅ Comprehensive usage insights

### **8.3 Business Metrics**
- ✅ Reduced support tickets about membership status
- ✅ Improved renewal rates
- ✅ Better user engagement analytics
- ✅ Enhanced user lifecycle management

## 9. Risk Mitigation

### **9.1 Technical Risks**
- **Data corruption:** Comprehensive backup and rollback procedures
- **API failures:** Fallback to cached data and graceful degradation
- **Performance issues:** Caching and optimization strategies
- **Security breaches:** Encryption and access controls

### **9.2 User Experience Risks**
- **Confusion during migration:** Clear communication and documentation
- **Unexpected expiration:** Grace periods and warning systems
- **Feature disruption:** Gradual rollout and feature flags
- **Privacy concerns:** Transparent data usage policies

## 10. Future Enhancements

### **10.1 Advanced Features (Phase 2)**
- **Multi-tier subscriptions:** Different feature sets per tier
- **Team management:** Organization-level memberships
- **API access tracking:** Detailed API usage analytics
- **Custom renewal periods:** Flexible subscription durations

### **10.2 Integration Opportunities**
- **Payment processors:** Stripe, PayPal integration
- **CRM systems:** Customer relationship management
- **Support systems:** Automated ticket creation
- **Marketing tools:** User segmentation and campaigns

---

## ✅ Implementation Readiness

This plan provides a comprehensive roadmap for implementing robust membership tracking and automatic key expiration management. The system is designed to be:

- **Backward compatible** with existing pro key system
- **Scalable** for future growth and features
- **User-friendly** with clear status and analytics
- **Secure** with proper data protection
- **Maintainable** with modular architecture

**Next Steps:**
1. Review and approve the plan
2. Set up development environment
3. Begin Phase 1 implementation
4. Establish testing procedures
5. Plan migration strategy 