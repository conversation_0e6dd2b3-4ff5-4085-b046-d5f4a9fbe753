// Comprehensive Method Comparison and Fix Script
// This script compares the original popup.js with the refactored version
// and ensures all functionality is properly implemented

console.log('🔍 Starting comprehensive method comparison...');

// Original popup.js methods that must be preserved
const REQUIRED_METHODS = {
    // Core functionality
    'init': 'async init()',
    'loadApiKey': 'async loadApiKey()',
    'saveApiKey': 'async saveApiKey(apiKey)',
    'updateUI': 'updateUI()',
    'updateApiStatus': 'updateApiStatus()',
    'showSection': 'showSection(sectionId)',
    'showError': 'showError(message)',
    'showSuccess': 'showSuccess(message)',
    'showMembershipWarning': 'showMembershipWarning(warning)',
    
    // Analysis methods
    'analyzeSelection': 'async analyzeSelection()',
    'analyzePage': 'async analyzePage()',
    'runCustomAnalysis': 'async runCustomAnalysis()',
    'performAnalysis': 'async performAnalysis(prompt, analysisType)',
    'displayResults': 'displayResults(result, analysisType, date)',
    'formatAnalysisContent': 'formatAnalysisContent(content, summary)',
    'formatToolCalls': 'formatToolCalls(toolCalls)',
    'convertToMarkdown': 'convertToMarkdown(analysisData)',
    'copyResults': 'async copyResults()',
    'exportResults': 'exportResults()',
    'analyzeSelectionWithText': 'async analyzeSelectionWithText(selectedText)',
    'analyzePageWithData': 'async analyzePageWithData(pageData)',
    
    // Data management
    'saveAnalysis': 'async saveAnalysis(analysisType, result)',
    'loadAndDisplayAnalysis': 'async loadAndDisplayAnalysis()',
    'viewAnalysisFromHistory': 'async viewAnalysisFromHistory(analysisId)',
    'deleteAnalysis': 'async deleteAnalysis(analysisId)',
    
    // Pro features
    'handleCustomAnalysisClick': 'async handleCustomAnalysisClick()',
    'handleProKeyValidation': 'async handleProKeyValidation()',
    'handlePromptManagerClick': 'async handlePromptManagerClick()',
    'updateProKeyStatus': 'updateProKeyStatus(status, message)',
    
    // Prompt management
    'loadPromptManagement': 'async loadPromptManagement()',
    'loadSavedPromptsSelect': 'async loadSavedPromptsSelect()',
    'loadSelectedPrompt': 'async loadSelectedPrompt()',
    'saveCurrentPrompt': 'async saveCurrentPrompt()',
    'refreshPromptList': 'async refreshPromptList()',
    'displayPromptList': 'displayPromptList(prompts, pagination)',
    'searchPrompts': 'async searchPrompts(query)',
    'sortPrompts': 'async sortPrompts(sortBy)',
    'loadTagFilters': 'async loadTagFilters()',
    'filterByTag': 'async filterByTag(tag)',
    'usePrompt': 'async usePrompt(promptId)',
    'togglePromptPin': 'async togglePromptPin(promptId)',
    'openPromptEditor': 'async openPromptEditor(prompt)',
    'editPrompt': 'async editPrompt(promptId)',
    'closePromptEditor': 'closePromptEditor()',
    'savePromptEdit': 'async savePromptEdit()',
    'deletePromptConfirm': 'async deletePromptConfirm(promptId)',
    'deletePrompt': 'async deletePrompt(promptId)',
    'copyPromptContent': 'async copyPromptContent(promptId)',
    'exportPrompts': 'async exportPrompts()',
    'importPrompts': 'async importPrompts(file)',
    
    // Settings and integrations
    'loadSettingsContent': 'async loadSettingsContent()',
    'loadMembershipInfo': 'async loadMembershipInfo()',
    'loadTelegramSettings': 'async loadTelegramSettings()',
    'loadDiscordSettings': 'async loadDiscordSettings()',
    'setupTelegramEventListeners': 'setupTelegramEventListeners()',
    'setupDiscordEventListeners': 'setupDiscordEventListeners()',
    'saveTelegramSettings': 'async saveTelegramSettings()',
    'testTelegramConnection': 'async testTelegramConnection()',
    'editTelegramSettings': 'async editTelegramSettings()',
    'clearTelegramSettings': 'async clearTelegramSettings()',
    'saveDiscordSettings': 'async saveDiscordSettings()',
    'testDiscordConnection': 'async testDiscordConnection()',
    'editDiscordSettings': 'async editDiscordSettings()',
    'clearDiscordSettings': 'async clearDiscordSettings()',
    'handleTelegramAutoSendToggle': 'async handleTelegramAutoSendToggle(enabled)',
    'handleDiscordAutoSendToggle': 'async handleDiscordAutoSendToggle(enabled)',
    
    // Auto-send functionality
    'handleAutoSend': 'async handleAutoSend(analysisType, result)',
    'handleTelegramAutoSend': 'async handleTelegramAutoSend(analysisData)',
    'handleDiscordAutoSend': 'async handleDiscordAutoSend(analysisData)',
    
    // UI helpers
    'showHelp': 'showHelp()',
    'showAbout': 'showAbout()',
    'escapeHtml': 'escapeHtml(text)',
    
    // Event handling
    'setupEventListeners': 'setupEventListeners()',
    'setupMessageListeners': 'setupMessageListeners()',
    'setupEventDelegation': 'setupEventDelegation()',
    'checkPendingAnalysis': 'async checkPendingAnalysis()',
    'checkContextMenuActions': 'async checkContextMenuActions()',
    
    // Key management
    'showKeyManagementModal': 'async showKeyManagementModal()',
    'setupKeyManagementEventListeners': 'setupKeyManagementEventListeners()',
    'handleNewKeyValidation': 'async handleNewKeyValidation()',
    'updateNewKeyStatus': 'updateNewKeyStatus(status, message)',
    'closeKeyManagementModal': 'closeKeyManagementModal()',
    
    // Integration sending
    'sendAnalysisToTelegram': 'async sendAnalysisToTelegram(analysisId)',
    'sendAnalysisToDiscord': 'async sendAnalysisToDiscord(analysisId)'
};

async function checkMethodImplementation() {
    const results = {
        missing: [],
        implemented: [],
        delegated: [],
        errors: []
    };
    
    if (!window.analyzer) {
        results.errors.push('No analyzer instance found');
        return results;
    }
    
    console.log('🔍 Checking method implementation...');
    
    for (const [methodName, signature] of Object.entries(REQUIRED_METHODS)) {
        try {
            // Check if method exists on main analyzer
            if (typeof window.analyzer[methodName] === 'function') {
                results.implemented.push(methodName);
                continue;
            }
            
            // Check if method exists on controller managers
            let found = false;
            const managers = [
                'analysisManager',
                'dataManager', 
                'uiManager',
                'settingsManager',
                'promptUIManager',
                'integrationManager',
                'autoSendManager',
                'eventManager'
            ];
            
            for (const manager of managers) {
                if (window.analyzer.controller?.[manager]?.[methodName]) {
                    results.delegated.push(`${methodName} -> ${manager}`);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                results.missing.push(methodName);
            }
            
        } catch (error) {
            results.errors.push(`Error checking ${methodName}: ${error.message}`);
        }
    }
    
    return results;
}

async function validateCriticalMethods() {
    console.log('🧪 Testing critical methods...');
    const criticalTests = [];
    
    // Test analysis flow
    try {
        if (window.analyzer.analyzeSelection) {
            criticalTests.push({ method: 'analyzeSelection', status: 'available' });
        } else if (window.analyzer.controller?.analysisManager?.analyzeSelection) {
            criticalTests.push({ method: 'analyzeSelection', status: 'delegated' });
        } else {
            criticalTests.push({ method: 'analyzeSelection', status: 'missing' });
        }
    } catch (error) {
        criticalTests.push({ method: 'analyzeSelection', status: 'error', error: error.message });
    }
    
    // Test data saving
    try {
        if (window.analyzer.saveAnalysis) {
            criticalTests.push({ method: 'saveAnalysis', status: 'available' });
        } else if (window.analyzer.controller?.dataManager?.saveAnalysis) {
            criticalTests.push({ method: 'saveAnalysis', status: 'delegated' });
        } else {
            criticalTests.push({ method: 'saveAnalysis', status: 'missing' });
        }
    } catch (error) {
        criticalTests.push({ method: 'saveAnalysis', status: 'error', error: error.message });
    }
    
    // Test UI management
    try {
        if (window.analyzer.showSection) {
            criticalTests.push({ method: 'showSection', status: 'available' });
        } else if (window.analyzer.controller?.uiManager?.showSection) {
            criticalTests.push({ method: 'showSection', status: 'delegated' });
        } else {
            criticalTests.push({ method: 'showSection', status: 'missing' });
        }
    } catch (error) {
        criticalTests.push({ method: 'showSection', status: 'error', error: error.message });
    }
    
    return criticalTests;
}

// Main comparison function
async function runComparison() {
    console.log('🚀 Running comprehensive method comparison...');
    
    const methodCheck = await checkMethodImplementation();
    const criticalCheck = await validateCriticalMethods();
    
    console.log('📊 RESULTS:');
    console.log('='.repeat(50));
    
    console.log(`✅ Implemented methods: ${methodCheck.implemented.length}`);
    methodCheck.implemented.forEach(method => console.log(`  ✓ ${method}`));
    
    console.log(`🔄 Delegated methods: ${methodCheck.delegated.length}`);
    methodCheck.delegated.forEach(method => console.log(`  → ${method}`));
    
    console.log(`❌ Missing methods: ${methodCheck.missing.length}`);
    methodCheck.missing.forEach(method => console.log(`  ✗ ${method}`));
    
    console.log(`⚠️ Errors: ${methodCheck.errors.length}`);
    methodCheck.errors.forEach(error => console.log(`  ! ${error}`));
    
    console.log('\n🔍 Critical Method Status:');
    criticalCheck.forEach(test => {
        const icon = test.status === 'available' ? '✅' : 
                    test.status === 'delegated' ? '🔄' : 
                    test.status === 'missing' ? '❌' : '⚠️';
        console.log(`  ${icon} ${test.method}: ${test.status}`);
        if (test.error) console.log(`    Error: ${test.error}`);
    });
    
    // Store results for further analysis
    window.methodComparisonResults = {
        methodCheck,
        criticalCheck,
        totalMethods: Object.keys(REQUIRED_METHODS).length
    };
    
    return window.methodComparisonResults;
}

// Auto-run the comparison
runComparison().then(results => {
    console.log('\n💡 Results stored in window.methodComparisonResults');
    console.log('💡 Run window.methodComparisonResults to see detailed results');
}).catch(error => {
    console.error('❌ Comparison failed:', error);
});
