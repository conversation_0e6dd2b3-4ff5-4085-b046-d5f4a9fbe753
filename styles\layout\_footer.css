/* Footer Styles */
.footer {
    padding: 20px;
    border-top: 1px solid #2C2738;
    background: #141021;
    flex-shrink: 0; /* Prevents footer from shrinking if content is too large */
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 24px;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #B2AFC5;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: #F9F9F9;
    background: #1D1A2A;
    transform: translateY(-1px);
}

.footer-links a svg {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.footer-links a:hover svg {
    opacity: 1;
}
