/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #141021; /* Deep Violet / Midnight Purple */
    color: #F9F9F9; /* High Contrast White */
    line-height: 1.6;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

.container {
    width: 520px;
    min-height: 680px;
    background: #141021;
    border-radius: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Styles for the main content area to make footer stick to bottom */
.main-content-area {
    flex-grow: 1; /* Allows this area to expand and push footer down */
    overflow-y: visible; /* Allow content to expand naturally without scrollbars */
    display: flex; /* Added to allow its children (sections) to be managed if needed, e.g. centering */
    flex-direction: column; /* Sections stack vertically */
    min-height: 0; /* Important for flex children that might scroll or have min-heights themselves */
    /* padding-bottom: 20px; /* Optional: if sections need more space from footer */
                           /* Current sections have their own padding, so this might not be needed. */
}
