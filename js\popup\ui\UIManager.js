/**
 * UI Manager
 * Handles all DOM manipulation, section switching, and notifications
 */
import { BaseManager } from '../core/BaseManager.js';
import {
    setProKey,
    getMaskedProKey,
    getDetailedProStatus,
    removePro<PERSON>ey,
    refreshProStatus
} from '../../../js/user/proStatus.js';

export class UIManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.sections = [
            'apiKeySection', 'actionsSection', 'customForm', 'scrapeForm', 'resultsSection', 
            'loadingSection', 'analysisHistorySection', 'helpSection', 'aboutSection', 
            'upgradeSection', 'promptManagementSection', 'settingsSection'
        ];
    }

    async init() {
        await super.init();
        // UI Manager doesn't need async initialization
    }

    /**
     * Update main UI based on API key status
     */
    updateUI() {
        const hasApiKey = !!this.controller.apiKey;
        
        // Only update API status, don't force section display
        // Section display should be handled by navigation system
        if (hasApiKey) {
            this.updateApiStatus();
        }
        
        // Update the API status indicator visibility
        const apiKeySection = document.getElementById('apiKeySection');
        const actionsSection = document.getElementById('actionsSection');
        
        // Make sure both sections exist but let navigation control which is shown
        if (apiKeySection && actionsSection) {
            // Don't force display here - let the navigation system handle it
            // This prevents overriding the current navigation state
        }
    }

    /**
     * Update API status indicator
     */
    updateApiStatus() {
        const statusElement = document.getElementById('apiStatus');
        if (!statusElement) return;
        
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');
        
        if (this.controller.apiKey) {
            indicator?.classList.add('connected');
            if (text) text.textContent = 'API Key configured';
        } else {
            indicator?.classList.remove('connected');
            if (text) text.textContent = 'API Key not configured';
        }
    }

    /**
     * Show a specific section and hide all others
     */
    showSection(sectionId) {
        // Hide all sections
        this.sections.forEach(id => {
            const el = document.getElementById(id);
            if (el) {
                el.style.display = 'none';
            }
        });
        
        // Hide modal if open
        const modal = document.getElementById('promptEditorModal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        // Show target section
        const targetEl = document.getElementById(sectionId);
        if (targetEl) {
            targetEl.style.display = 'block';
        }
        
        // Load settings content if showing settings
        if (sectionId === 'settingsSection') {
            this.controller.settingsManager?.loadSettingsContent();
        }
    }

    /**
     * Show error notification
     */
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <div style="background: #dc3545; color: white; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px;">
                ❌ ${message}
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    /**
     * Show success notification
     */
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification';
        successDiv.innerHTML = `
            <div style="background: #28a745; color: white; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px;">
                ✅ ${message}
            </div>
        `;
        
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * Show membership warning notification
     */
    showMembershipWarning(warning) {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'membership-warning-notification';
        warningDiv.innerHTML = `
            <div style="background: ${warning.urgent ? '#dc3545' : '#ffc107'}; color: ${warning.urgent ? 'white' : '#212529'}; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px; display: flex; align-items: center; justify-content: space-between;">
                <span>${warning.message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0 5px;">×</button>
            </div>
        `;
        
        document.body.appendChild(warningDiv);
        
        // Auto-hide after 10 seconds for non-urgent warnings
        if (!warning.urgent) {
            setTimeout(() => {
                if (warningDiv && warningDiv.parentNode) {
                    warningDiv.remove();
                }
            }, 10000);
        }
    }

    /**
     * Update Pro key status display
     */
    updateProKeyStatus(status, message) {
        const proKeyStatus = document.getElementById('proKeyStatus');
        if (!proKeyStatus) return;
        
        const statusText = proKeyStatus.querySelector('.status-text');
        
        // Remove existing status classes
        proKeyStatus.classList.remove('validating', 'valid', 'invalid');
        
        // Add new status class
        if (status !== 'default') {
            proKeyStatus.classList.add(status);
        }
        
        // Update message
        if (statusText) {
            statusText.textContent = message;
        }
    }

    /**
     * Update new key status in key management modal
     */
    updateNewKeyStatus(status, message) {
        const newKeyStatus = document.getElementById('newKeyStatus');
        if (!newKeyStatus) return;
        
        const statusText = newKeyStatus.querySelector('.status-text');
        
        // Remove existing status classes
        newKeyStatus.classList.remove('validating', 'valid', 'invalid');
        
        // Add new status class
        if (status !== 'default') {
            newKeyStatus.classList.add(status);
        }
        
        // Update message
        if (statusText) {
            statusText.textContent = message;
        }
    }

    /**
     * Show help section
     */
    showHelp() {
        this.showSection('helpSection');
    }

    /**
     * Show about section
     */
    showAbout() {
        this.showSection('aboutSection');
    }

    /**
     * Close prompt editor modal
     */
    closePromptEditor() {
        const modal = document.getElementById('promptEditorModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Close key management modal
     */
    closeKeyManagementModal() {
        const modal = document.getElementById('keyManagementModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Show key management modal
     */
    async showKeyManagementModal() {
        const modal = document.getElementById('keyManagementModal');
        const content = document.getElementById('keyManagementContent');

        if (!modal || !content) return;

        try {
            // Get current key info
            const maskedKey = await getMaskedProKey();
            const detailedStatus = await getDetailedProStatus();

            content.innerHTML = `
                <div class="key-management-content">
                    <div class="current-key-section">
                        <h4>Current Pro Key</h4>
                        <div class="key-info">
                            <div class="key-info">
                                <label>Key:</label>
                                <span class="masked-key">${maskedKey || 'No key configured'}</span>
                            </div>
                            ${detailedStatus.isPro ? `
                                <div class="key-status-info">
                                    <span class="status-badge active">✅ Valid</span>
                                    ${detailedStatus.membershipDetails ? `
                                        <span class="key-expires">Expires: ${new Date(detailedStatus.membershipDetails.expiresAt).toLocaleDateString()}</span>
                                    ` : ''}
                                </div>
                            ` : `
                                <div class="key-status-info">
                                    <span class="status-badge invalid">❌ ${detailedStatus.expired ? 'Expired' : 'Invalid'}</span>
                                </div>
                            `}
                        </div>
                    </div>

                    <div class="key-actions-section">
                        <h4>Key Management</h4>
                        <div class="key-actions" style="display: flex; flex-direction: column; align-items: center; gap: 12px; margin-top: 12px;">
                            ${maskedKey ? `
                                <button id="testCurrentKey" class="btn btn-outline btn-sm" style="width: 80%; max-width: 200px;">
                                    🔄 Test Current Key
                                </button>
                                <button id="removeProKey" class="btn btn-danger btn-sm" style="width: 80%; max-width: 200px;">
                                    🗑️ Remove Key
                                </button>
                            ` : ''}
                            <button id="changeProKey" class="btn btn-primary btn-sm" style="width: 80%; max-width: 200px;">
                                🔑 ${maskedKey ? 'Change Key' : 'Add Key'}
                            </button>
                        </div>
                    </div> 

                    <div id="newKeySection" class="new-key-section" style="display: none;">
                        <h4>Enter New Pro Key</h4>
                        <div class="form-group">
                            <input type="password" id="newProKeyInput" placeholder="Enter your new pro key">
                        </div>
                        <div class="form-actions">
                            <button id="validateNewKey" class="btn btn-primary btn-sm">
                                ✅ Validate Key
                            </button>
                            <button id="cancelKeyChange" class="btn btn-outline btn-sm">
                                ❌ Cancel
                            </button>
                        </div>
                    </div>
                </div>
            `;

            this.setupKeyManagementEventListeners();
            modal.style.display = 'block';

        } catch (error) {
            console.error('Error showing key management modal:', error);
            this.showError('Failed to load key management');
        }
    }

    /**
     * Utility method to escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Set button loading state
     */
    setButtonLoading(buttonId, isLoading, loadingText = 'Loading...', originalText = null) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (isLoading) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = `⏳ ${loadingText}`;
        } else {
            button.disabled = false;
            button.innerHTML = originalText || button.dataset.originalText || button.innerHTML;
        }
    }

    /**
     * Get element safely
     */
    getElement(id) {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`Element with id '${id}' not found`);
        }
        return element;
    }

    /**
     * Set element display
     */
    setElementDisplay(id, display) {
        const element = this.getElement(id);
        if (element) {
            element.style.display = display;
        }
    }

    /**
     * Set element content safely
     */
    setElementContent(id, content, isHTML = false) {
        const element = this.getElement(id);
        if (element) {
            if (isHTML) {
                element.innerHTML = content;
            } else {
                element.textContent = content;
            }
        }
    }

    /**
     * Setup key management event listeners
     */
    setupKeyManagementEventListeners() {
        // Change/Add Key button
        const changeKeyBtn = document.getElementById('changeProKey');
        if (changeKeyBtn) {
            changeKeyBtn.addEventListener('click', () => {
                const newKeySection = document.getElementById('newKeySection');
                if (newKeySection) {
                    newKeySection.style.display = 'block';
                    const input = document.getElementById('newProKeyInput');
                    if (input) input.focus();
                }
            });
        }

        // Remove Key button
        const removeKeyBtn = document.getElementById('removeProKey');
        if (removeKeyBtn) {
            removeKeyBtn.addEventListener('click', async () => {
                if (confirm('Are you sure you want to remove your Pro key? This will disable all Pro features.')) {
                    const success = await removeProKey();
                    if (success) {
                        this.showSuccess('Pro key removed successfully');
                        this.closeKeyManagementModal();
                        this.updateUI();
                    } else {
                        this.showError('Failed to remove Pro key');
                    }
                }
            });
        }

        // Test Current Key button
        const testKeyBtn = document.getElementById('testCurrentKey');
        if (testKeyBtn) {
            testKeyBtn.addEventListener('click', async () => {
                testKeyBtn.disabled = true;
                testKeyBtn.textContent = '🔄 Testing...';

                try {
                    const status = await refreshProStatus();
                    if (status.isPro) {
                        this.showSuccess('✅ Pro key is valid and active');
                    } else {
                        this.showError(`❌ Pro key validation failed: ${status.message}`);
                    }
                } catch (error) {
                    this.showError('Failed to test Pro key');
                } finally {
                    testKeyBtn.disabled = false;
                    testKeyBtn.textContent = '🔄 Test Current Key';
                }
            });
        }

        // Validate New Key button
        const validateNewBtn = document.getElementById('validateNewKey');
        if (validateNewBtn) {
            validateNewBtn.addEventListener('click', async () => {
                await this.handleNewKeyValidation();
            });
        }

        // Cancel Key Change button
        const cancelBtn = document.getElementById('cancelKeyChange');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                const newKeySection = document.getElementById('newKeySection');
                if (newKeySection) {
                    newKeySection.style.display = 'none';
                    const input = document.getElementById('newProKeyInput');
                    if (input) input.value = '';
                }
            });
        }
    }

    /**
     * Handle new key validation
     */
    async handleNewKeyValidation() {
        const newKeyInput = document.getElementById('newProKeyInput');
        const validateBtn = document.getElementById('validateNewKey');

        const newKey = newKeyInput?.value.trim();

        if (!newKey) {
            this.showError('Please enter a pro key');
            return;
        }

        try {
            validateBtn.disabled = true;
            validateBtn.textContent = '⏳ Validating...';

            const result = await setProKey(newKey);

            if (result.success && result.isPro) {
                this.showSuccess('✅ Pro key validated and saved successfully!');
                this.closeKeyManagementModal();
                this.updateUI();
            } else {
                this.showError(`❌ Invalid pro key: ${result.message}`);
            }

        } catch (error) {
            console.error('Error validating new key:', error);
            this.showError('Failed to validate pro key');
        } finally {
            validateBtn.disabled = false;
            validateBtn.textContent = '✅ Validate Key';
        }
    }

    /**
     * Show membership warning notification
     */
    showMembershipWarning(warning) {
        // Create membership warning notification
        const warningDiv = document.createElement('div');
        warningDiv.className = 'membership-warning-notification';
        warningDiv.innerHTML = `
            <div style="background: ${warning.urgent ? '#dc3545' : '#ffc107'}; color: ${warning.urgent ? 'white' : '#212529'}; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px; display: flex; align-items: center; justify-content: space-between;">
                <span>${warning.message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0 5px;">×</button>
            </div>
        `;

        // Insert at the top of the container
        const container = document.querySelector('.container');
        if (container) {
            container.insertBefore(warningDiv, container.firstChild);

            // Auto-remove after 10 seconds if not urgent
            if (!warning.urgent) {
                setTimeout(() => {
                    if (warningDiv.parentNode) {
                        warningDiv.remove();
                    }
                }, 10000);
            }
        }
    }

    /**
     * Format analysis content for display
     */
    formatAnalysisContent(content, summary = false) {
        if (typeof content === 'string') {
            return summary ? content.substring(0, 100) + '...' : content;
        } else if (typeof content === 'object' && content !== null) {
            // More robust handling for object content
            let formatted = '';
            if (content.content) {
                formatted += content.content;
            }
            if (content.tool_calls) {
                formatted += `\n\nTool Calls:\n${this.formatToolCalls(content.tool_calls)}`;
            }
            if (content.usage) {
                formatted += `\n\nUsage: ${JSON.stringify(content.usage)}`;
            }

            return summary ? formatted.substring(0, 100) + '...' : formatted;
        } else {
            return summary ? 'Analysis result...' : 'No content available';
        }
    }

    /**
     * Format tool calls for display
     */
    formatToolCalls(toolCalls) {
        if (!Array.isArray(toolCalls)) return '';

        return toolCalls.map(call => {
            return `- ${call.function?.name || 'Unknown'}: ${call.function?.arguments || ''}`;
        }).join('\n');
    }
}
