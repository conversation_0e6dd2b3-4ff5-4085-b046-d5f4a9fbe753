// Simple membership expiration checker
import { checkMembershipExpiration, getDetailedProStatus } from '../user/proStatus.js';
import { MEMBERSHIP_CONFIG } from '../../config.js';

/**
 * Check for membership expiration and show notifications
 * @returns {Promise<Object>} - Check result with actions taken
 */
export async function checkAndNotifyExpiration() {
    try {
        const expirationCheck = await checkMembershipExpiration();
        
        if (!expirationCheck.checked) {
            return {
                success: false,
                message: 'Failed to check expiration status'
            };
        }
        
        // Get detailed status for notification logic
        const detailedStatus = await getDetailedProStatus();
        
        // Handle expired memberships
        if (expirationCheck.expired) {
            await showExpirationNotification('expired', {
                message: 'Your Pro membership has expired',
                daysRemaining: 0
            });
            
            return {
                success: true,
                expired: true,
                message: 'Membership expired - user notified',
                actionTaken: 'Showed expiration notification'
            };
        }
        
        // Handle expiration warnings
        if (detailedStatus.expirationWarning) {
            const warning = detailedStatus.expirationWarning;
            await showExpirationNotification('warning', warning);
            
            return {
                success: true,
                expired: false,
                warning: true,
                daysRemaining: warning.daysRemaining,
                message: `Warning shown - ${warning.daysRemaining} days remaining`,
                actionTaken: 'Showed expiration warning'
            };
        }
        
        return {
            success: true,
            expired: false,
            warning: false,
            message: 'No expiration issues detected',
            actionTaken: 'No action needed'
        };
        
    } catch (error) {
        console.error('Error checking membership expiration:', error);
        return {
            success: false,
            message: 'Error during expiration check'
        };
    }
}

/**
 * Show expiration notification to user
 * @param {string} type - Notification type ('warning' or 'expired')
 * @param {Object} details - Notification details
 */
async function showExpirationNotification(type, details) {
    try {
        let title, message, iconUrl;
        
        if (type === 'expired') {
            title = 'HustlePlug Pro - Membership Expired';
            message = 'Your Pro membership has expired. Upgrade to continue using Pro features.';
            iconUrl = 'icons/icon-48.png';
        } else if (type === 'warning') {
            title = 'HustlePlug Pro - Membership Expiring Soon';
            message = details.message;
            iconUrl = 'icons/icon-48.png';
        }
        
        // Create browser notification
        if (chrome.notifications) {
            await chrome.notifications.create({
                type: 'basic',
                iconUrl: iconUrl,
                title: title,
                message: message,
                priority: details.urgent ? 2 : 1
            });
        }
        
        // Store notification history for UI display
        await storeNotificationHistory(type, details);
        
    } catch (error) {
        console.warn('Failed to show expiration notification:', error);
    }
}

/**
 * Store notification in history for UI display
 * @param {string} type - Notification type
 * @param {Object} details - Notification details
 */
async function storeNotificationHistory(type, details) {
    try {
        const notification = {
            id: Date.now().toString(),
            type: type,
            timestamp: new Date().toISOString(),
            details: details,
            shown: true
        };
        
        // Get existing notifications
        const result = await chrome.storage.local.get(['membershipNotifications']);
        const notifications = result.membershipNotifications || [];
        
        // Add new notification and keep only last 10
        notifications.unshift(notification);
        const trimmedNotifications = notifications.slice(0, 10);
        
        await chrome.storage.local.set({
            membershipNotifications: trimmedNotifications
        });
        
    } catch (error) {
        console.warn('Failed to store notification history:', error);
    }
}

/**
 * Get recent membership notifications for UI display
 * @returns {Promise<Array>} - Array of recent notifications
 */
export async function getRecentNotifications() {
    try {
        const result = await chrome.storage.local.get(['membershipNotifications']);
        return result.membershipNotifications || [];
    } catch (error) {
        console.warn('Failed to get recent notifications:', error);
        return [];
    }
}

/**
 * Clear notification history
 * @returns {Promise<boolean>} - Success status
 */
export async function clearNotificationHistory() {
    try {
        await chrome.storage.local.remove(['membershipNotifications']);
        return true;
    } catch (error) {
        console.warn('Failed to clear notification history:', error);
        return false;
    }
}

/**
 * Check if daily expiration check should run
 * @returns {Promise<boolean>} - Whether check should run
 */
export async function shouldRunDailyCheck() {
    try {
        const result = await chrome.storage.local.get(['lastExpirationCheck']);
        const lastCheck = result.lastExpirationCheck;
        
        if (!lastCheck) {
            return true;
        }
        
        const lastCheckDate = new Date(lastCheck);
        const now = new Date();
        const daysDiff = Math.floor((now - lastCheckDate) / (1000 * 60 * 60 * 24));
        
        return daysDiff >= 1;
        
    } catch (error) {
        console.warn('Error checking daily check status:', error);
        return true; // Default to running check on error
    }
}

/**
 * Mark daily check as completed
 * @returns {Promise<boolean>} - Success status
 */
export async function markDailyCheckCompleted() {
    try {
        await chrome.storage.local.set({
            lastExpirationCheck: new Date().toISOString()
        });
        return true;
    } catch (error) {
        console.warn('Failed to mark daily check completed:', error);
        return false;
    }
}

/**
 * Run daily membership check (called from popup or background)
 * @returns {Promise<Object>} - Check result
 */
export async function runDailyMembershipCheck() {
    try {
        const shouldRun = await shouldRunDailyCheck();
        
        if (!shouldRun) {
            return {
                success: true,
                skipped: true,
                message: 'Daily check already completed today'
            };
        }
        
        const checkResult = await checkAndNotifyExpiration();
        await markDailyCheckCompleted();
        
        return {
            success: true,
            skipped: false,
            checkResult: checkResult,
            message: 'Daily membership check completed'
        };
        
    } catch (error) {
        console.error('Error running daily membership check:', error);
        return {
            success: false,
            message: 'Failed to run daily membership check'
        };
    }
} 