/**
 * Analysis Manager
 * Handles all analysis operations (selection, page, custom, URL scraping) and result processing
 */
import { BaseManager } from '../core/BaseManager.js';
import { FirecrawlScraper } from '../../integrations/firecrawl.js';

export class AnalysisManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Initialize Firecrawl scraper
        this.firecrawlScraper = new FirecrawlScraper();
    }

    /**
     * Analyze selected text from the current tab
     */
    async analyzeSelection() {
        try {
            this.controller.uiManager.showSection('loadingSection');
            
            // Get selected text from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => window.getSelection().toString()
            });
            
            const selectedText = results[0].result;
            
            if (!selectedText || selectedText.trim().length === 0) {
                this.controller.uiManager.showError('No text selected. Please select some text and try again.');
                this.controller.uiManager.showSection('actionsSection');
                return;
            }

            const prompt = `Please analyze the following selected text and provide a comprehensive, well-structured analysis:

Selected Text:
${selectedText}

Please format your response with clear sections and structure:

## Summary of Content
Provide a concise overview of the main topic and purpose.

## Key Insights
1. **Primary Finding**: [Main insight with supporting details]
2. **Secondary Finding**: [Additional important insight]
3. **Notable Patterns**: [Any patterns or trends identified]

## Recommendations
• Actionable next steps based on the analysis
• Relevant considerations or implications
• Potential applications or use cases

Please use clear formatting with numbered points, bullet points, and bold text for emphasis. Include specific metrics, percentages, or data points where relevant.`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing selection:', error);
            this.controller.uiManager.showError('Failed to analyze selected text');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze selected text with provided text (from content script)
     */
    async analyzeSelectionWithText(selectedText) {
        try {
            if (!selectedText || selectedText.trim().length === 0) {
                this.controller.uiManager.showError('No text provided for analysis.');
                return;
            }

            this.controller.uiManager.showSection('loadingSection');

            const prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:

Selected Text:
${selectedText}

Please provide a comprehensive analysis including:
1. Summary of the content
2. Key insights and takeaways
3. Any important details or patterns
4. Recommendations or next steps if applicable`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing provided text:', error);
            this.controller.uiManager.showError('Failed to analyze selected text');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze the entire page content
     */
    async analyzePage() {
        try {
            this.controller.uiManager.showSection('loadingSection');
            
            // Get page content from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => {
                    // Extract meaningful content from the page
                    const title = document.title;
                    const url = window.location.href;
                    const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
                    
                    // Get main content (try to avoid navigation, ads, etc.)
                    const contentSelectors = [
                        'main',
                        'article',
                        '.content',
                        '.post-content',
                        '.entry-content',
                        '#content',
                        '.main-content'
                    ];
                    
                    let mainContent = '';
                    for (const selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            mainContent = element.innerText;
                            break;
                        }
                    }
                    
                    // Fallback to body content if no main content found
                    if (!mainContent) {
                        mainContent = document.body.innerText;
                    }
                    
                    // Limit content length to avoid API limits
                    const maxLength = 8000;
                    if (mainContent.length > maxLength) {
                        mainContent = mainContent.substring(0, maxLength) + '...';
                    }
                    
                    return {
                        title,
                        url,
                        metaDescription,
                        content: mainContent
                    };
                }
            });
            
            const pageData = results[0].result;
            
            const prompt = `Please analyze the following webpage and provide comprehensive insights with clear structure:

Page Title: ${pageData.title}
URL: ${pageData.url}
Meta Description: ${pageData.metaDescription}

Page Content:
${pageData.content}

Please format your response with clear sections:

## Summary of Content
Brief overview of the page's main purpose and topic.

## Key Insights
1. **Content Quality**: Assessment of information quality and credibility
2. **Main Topics**: Primary themes and subjects discussed
3. **Target Audience**: Intended audience and use cases
4. **Content Structure**: How information is organized and presented

## Market Analysis (if applicable)
• Industry relevance and positioning
• Competitive landscape insights
• Market trends or data mentioned

## Recommendations
• Actionable takeaways for readers
• Potential improvements or considerations
• Related topics to explore further

Use clear formatting with numbered points, bullet points, and **bold text** for emphasis. Highlight any specific metrics, percentages, or data points with proper formatting.`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.controller.uiManager.showError('Failed to analyze page content');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Analyze page with provided data (from content script)
     */
    async analyzePageWithData(pageData) {
        try {
            if (!pageData || !pageData.content) {
                this.controller.uiManager.showError('No page content available for analysis.');
                return;
            }

            this.controller.uiManager.showSection('loadingSection');

            const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.controller.uiManager.showError('Failed to analyze page');
            this.controller.uiManager.showSection('actionsSection');
        }
    }

        /**
     * Run custom analysis with user-provided prompt
     */
    async runCustomAnalysis() {
        try {
            const prompt = document.getElementById('customPrompt').value.trim();
            const dataSource = document.querySelector('input[name="dataSource"]:checked').value;

            if (!prompt) {
                this.controller.uiManager.showError('Please enter an analysis prompt');
                return;
            }

            this.controller.uiManager.showSection('loadingSection');

            let data = '';
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (dataSource === 'selection') {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => window.getSelection().toString()
                });
                data = results[0].result;

                if (!data || data.trim().length === 0) {
                    this.controller.uiManager.showError('No text selected. Please select some text or choose "Full Page" option.');
                    this.controller.uiManager.showSection('customForm');
                    return;
                }
            } else if (dataSource === 'url') {
                // Check pro status for URL scraping
                const proStatus = await this.controller.checkProStatus();
                if (!proStatus.isPro) {
                    this.controller.uiManager.showError('🚫 URL Scraping is a Pro feature. Please upgrade to access this functionality.');
                    this.controller.navigateToSection('upgradeSection');
                    return;
                }

                // Handle URL scraping
                const urlInput = document.getElementById('scrapeUrl');
                const url = urlInput?.value.trim();
                
                if (!url) {
                    this.controller.uiManager.showError('Please enter a URL to scrape');
                    this.controller.uiManager.showSection('customForm');
                    return;
                }

                try {
                    const scrapeResult = await this.firecrawlScraper.scrapeWithRetry(url);
                    data = `Title: ${scrapeResult.title}\nURL: ${scrapeResult.sourceURL}\n\nContent:\n${scrapeResult.content}`;
                } catch (scrapeError) {
                    this.controller.uiManager.showError(`Failed to scrape URL: ${scrapeError.message}`);
                    this.controller.uiManager.showSection('customForm');
                    return;
                }
            } else {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => {
                        const title = document.title;
                        const content = document.body.innerText.substring(0, 8000);
                        return `Title: ${title}\n\nContent: ${content}`;
                    }
                });
                data = results[0].result;
            }

            const fullPrompt = `${prompt}

Data to analyze:
${data}`;

            await this.performAnalysis(fullPrompt, 'Custom Analysis');

        } catch (error) {
            console.error('Error running custom analysis:', error);
            this.controller.uiManager.showError('Failed to run custom analysis');
            this.controller.uiManager.showSection('customForm');
        }
    }

    /**
     * Analyze URL by scraping content first
     */
    async analyzeScrapeUrl() {
        try {
            // Check pro status first
            const proStatus = await this.controller.checkProStatus();
            if (!proStatus.isPro) {
                this.controller.uiManager.showError('🚫 Scrape & Analyze is a Pro feature. Please upgrade to access this functionality.');
                this.controller.navigateToSection('upgradeSection');
                return;
            }
            // Check both possible URL input fields
            const urlInput = document.getElementById('scrapeUrlDirect') || document.getElementById('scrapeUrl');
            const url = urlInput?.value.trim();

            if (!url) {
                this.controller.uiManager.showError('Please enter a URL to scrape and analyze');
                return;
            }

            // Set button loading state
            const scrapeBtn = document.getElementById('analyzeScrapeUrl');
            if (scrapeBtn) {
                scrapeBtn.classList.add('loading');
                scrapeBtn.disabled = true;
                scrapeBtn.innerHTML = '<span class="scrape-icon">⏳</span>Scraping...';
            }

            this.controller.uiManager.showSection('loadingSection');

            console.log('🔍 Starting URL scrape and analysis for:', url);

            // Scrape the URL content
            const scrapeResult = await this.firecrawlScraper.scrapeWithRetry(url);

            // Create analysis prompt for scraped content
            const prompt = `Please analyze the following web page content and provide comprehensive insights with clear structure:

Page Title: ${scrapeResult.title}
URL: ${scrapeResult.sourceURL}
Scraped At: ${scrapeResult.scrapedAt}

Page Content:
${scrapeResult.content}

Please format your response with clear sections:

## Summary of Content
Brief overview of the page's main purpose and topic.

## Key Insights
1. **Content Quality**: Assessment of information quality and credibility
2. **Main Topics**: Primary themes and subjects discussed
3. **Target Audience**: Intended audience and use cases
4. **Content Structure**: How information is organized and presented

## Market Analysis (if applicable)
• Industry relevance and positioning
• Competitive landscape insights
• Market trends or data mentioned

## Recommendations
• Actionable takeaways for readers
• Potential improvements or considerations
• Related topics to explore further

Use clear formatting with numbered points, bullet points, and **bold text** for emphasis. Highlight any specific metrics, percentages, or data points with proper formatting.`;

            await this.performAnalysis(prompt, 'URL Scrape Analysis');

        } catch (error) {
            console.error('Error analyzing scraped URL:', error);
            this.controller.uiManager.showError(`Failed to scrape and analyze URL: ${error.message}`);
            this.controller.uiManager.showSection('scrapeForm');
        } finally {
            // Reset button state
            const scrapeBtn = document.getElementById('analyzeScrapeUrl');
            if (scrapeBtn) {
                scrapeBtn.classList.remove('loading');
                scrapeBtn.disabled = false;
                scrapeBtn.innerHTML = '<span class="scrape-icon">🔥</span>Scrape & Analyze';
            }
        }
    }

    /**
     * Core analysis function - sends request to background script
     */
    async performAnalysis(prompt, analysisType) {
        try {
            console.log('🚀 Starting analysis:', analysisType);

            const response = await chrome.runtime.sendMessage({
                action: 'performAnalysis',
                data: {
                    prompt,
                    analysisType
                }
            });

            console.log('📡 Background script response:', response);

            if (response && response.error) {
                throw new Error(response.error);
            }

            if (!response || !response.result) {
                throw new Error('No result received from background script');
            }

            this.controller.currentAnalysis = {
                type: analysisType,
                prompt: prompt,
                result: response.result,
                timestamp: new Date().toISOString()
            };

            console.log('💾 Saving analysis...');
            // Save the new analysis
            await this.controller.dataManager.saveAnalysis(analysisType, response.result);

            console.log('📊 Displaying results...');
            this.displayResults(response.result, analysisType);

            // Handle auto-send after successful analysis
            try {
                await this.controller.autoSendManager.handleAutoSend(analysisType, response.result);
            } catch (autoSendError) {
                console.warn('Auto-send failed:', autoSendError);
                // Don't fail the whole analysis for auto-send issues
            }

        } catch (error) {
            console.error('❌ Analysis error:', error);
            this.controller.uiManager.showError(`Analysis failed: ${error.message}`);
            this.controller.uiManager.showSection('actionsSection');
        }
    }

    /**
     * Display analysis results in the UI with enhanced formatting
     */
    displayResults(result, analysisType, date = new Date()) {
        this.controller.currentAnalysis = {
            result: result,
            type: analysisType,
            timestamp: date.toISOString(),
        };

        const resultsContainer = document.getElementById('analysisResults');
        if (!resultsContainer) {
            console.error('Results container not found');
            return;
        }
        
        let content = '';
        if (typeof result === 'string') {
            content = result;
        } else if (result.content) {
            content = result.content;
        } else if (result.result) {
            content = typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2);
        } else {
            content = JSON.stringify(result, null, 2);
        }
        
        const formattedContent = this.formatAnalysisContent(content);
        
        // Create header with improved styling
        const headerHtml = `
            <div class="analysis-header">
                <h4 style="color: #F9F9F9; margin: 0 0 8px 0; font-size: 18px; font-weight: 600;">
                    ${this.getAnalysisIcon(analysisType)} ${analysisType}
                </h4>
                <div class="analysis-meta" style="display: flex; gap: 16px; font-size: 13px; color: #B2AFC5; margin-bottom: 16px;">
                    <span class="timestamp">📅 ${date.toLocaleString()}</span>
                    <span class="session">🔗 Session: ${this.controller.sessionId.slice(-8)}</span>
                </div>
            </div>
        `;
        
        // Combine header and formatted content
        resultsContainer.innerHTML = headerHtml + formattedContent + 
            (result.tool_calls && result.tool_calls.length > 0 ? this.formatToolCalls(result.tool_calls) : '');
        
        this.controller.uiManager.showSection('resultsSection');
    }

    /**
     * Get appropriate icon for analysis type
     */
    getAnalysisIcon(analysisType) {
        const icons = {
            'Text Selection Analysis': '📝',
            'Full Page Analysis': '🌐', 
            'Custom Analysis': '🔧',
            'URL Scrape Analysis': '🔗',
            'Crypto Analysis': '₿',
            'Market Analysis': '📊',
            'Financial Analysis': '💰'
        };
        return icons[analysisType] || '🔍';
    }

    /**
     * Format analysis content for display with enhanced structure and readability
     */
    formatAnalysisContent(content, summary = false) {
        if (typeof content === 'string') {
            if (summary) {
                return content.substring(0, 100) + '...';
            }
            
            // Apply structured formatting to string content
            return this.formatStructuredAnalysis(content);
        } else if (typeof content === 'object' && content !== null) {
            // More robust handling for object content
            let formatted = '';
            if (content.content) {
                formatted += this.formatStructuredAnalysis(content.content);
            }
            if (content.tool_calls) {
                formatted += `\n\n${this.formatToolCalls(content.tool_calls)}`;
            }
            return summary ? formatted.substring(0, 100) + '...' : formatted;
        }
        return summary ? 'No content preview'.substring(0, 100) + '...' : 'No content';
    }

    /**
     * Format analysis content with structured sections and enhanced readability
     */
    formatStructuredAnalysis(content) {
        if (!content || typeof content !== 'string') {
            return content;
        }

        // Clean up the content first
        let formatted = content.trim();

        // Apply structured formatting patterns
        formatted = this.enhanceTextStructure(formatted);
        formatted = this.highlightMetrics(formatted);
        formatted = this.formatSections(formatted);
        formatted = this.addVisualElements(formatted);

        return `<div class="structured-analysis">${formatted}</div>`;
    }

    /**
     * Enhance text structure with proper headers and sections
     */
    enhanceTextStructure(content) {
        // Convert numbered sections to proper headers
        content = content.replace(/^(\d+\.\s*\*\*[^*]+\*\*)/gm, '<div class="analysis-section-header">$1</div>');
        
        // Convert summary headers
        content = content.replace(/^(##\s*Summary[^#]*)/gm, '<div class="analysis-summary">📊 $1</div>');
        content = content.replace(/^(Summary of Content[^:]*:?)/gm, '<div class="analysis-summary">📊 $1</div>');
        
        // Convert key insights headers
        content = content.replace(/^(##\s*Key Insights[^#]*)/gm, '<div class="analysis-insights">🔍 $1</div>');
        content = content.replace(/^(Key Insights[^:]*:?)/gm, '<div class="analysis-insights">🔍 $1</div>');
        
        // Convert recommendations headers
        content = content.replace(/^(##\s*Recommendations[^#]*)/gm, '<div class="analysis-recommendations">💡 $1</div>');
        content = content.replace(/^(Recommendations[^:]*:?)/gm, '<div class="analysis-recommendations">💡 $1</div>');
        
        // Convert market analysis headers
        content = content.replace(/^(##\s*Market[^#]*)/gm, '<div class="analysis-market">📈 $1</div>');
        
        // Convert general numbered points to structured sections
        content = content.replace(/^(\d+\.\s*)([^:\n]+)(:?\s*)/gm, '<div class="analysis-point"><span class="point-number">$1</span><span class="point-title">$2</span>$3</div>');
        
        return content;
    }

    /**
     * Highlight metrics, percentages, and important numbers
     */
    highlightMetrics(content) {
        // Highlight percentages
        content = content.replace(/(\d+\.?\d*%)/g, '<span class="metric-percentage">$1</span>');
        
        // Highlight dollar amounts
        content = content.replace(/(\$[\d,]+\.?\d*[KMB]?)/g, '<span class="metric-currency">$1</span>');
        
        // Highlight large numbers with commas
        content = content.replace(/(\b\d{1,3}(?:,\d{3})+(?:\.\d+)?\b)/g, '<span class="metric-number">$1</span>');
        
        // Highlight time periods
        content = content.replace(/(\d+[hd]|\d+\s*(?:hour|day|week|month|year)s?)/gi, '<span class="metric-time">$1</span>');
        
        // Highlight rankings and positions
        content = content.replace(/(#\d+|rank\s*\d+|position\s*\d+)/gi, '<span class="metric-rank">$1</span>');
        
        return content;
    }

    /**
     * Format content sections with proper spacing and structure
     */
    formatSections(content) {
        // Add proper spacing around sections
        content = content.replace(/(<div class="analysis-[^"]+">)/g, '\n$1');
        content = content.replace(/(<\/div>)/g, '$1\n');
        
        // Format bullet points
        content = content.replace(/^[\s]*[-*]\s*(.+)$/gm, '<div class="analysis-bullet">• $1</div>');
        
        // Format bold text
        content = content.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        
        // Format italic text
        content = content.replace(/\*([^*]+)\*/g, '<em>$1</em>');
        
        // Format code blocks
        content = content.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Clean up extra whitespace
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
        content = content.replace(/^\s+|\s+$/g, '');
        
        return content;
    }

    /**
     * Add visual elements and status indicators
     */
    addVisualElements(content) {
        // Add status indicators for different types of content
        if (content.includes('stable') || content.includes('growth')) {
            content = content.replace(/(stable|growth)/gi, '<span class="status-positive">$1</span>');
        }
        
        if (content.includes('volatile') || content.includes('risk')) {
            content = content.replace(/(volatile|risk)/gi, '<span class="status-warning">$1</span>');
        }
        
        if (content.includes('decline') || content.includes('drop')) {
            content = content.replace(/(decline|drop)/gi, '<span class="status-negative">$1</span>');
        }
        
        // Add trend indicators
        content = content.replace(/(\+\d+\.?\d*%)/g, '<span class="trend-up">📈 $1</span>');
        content = content.replace(/(-\d+\.?\d*%)/g, '<span class="trend-down">📉 $1</span>');
        
        return content;
    }

    /**
     * Format tool calls for display with enhanced styling
     */
    formatToolCalls(toolCalls) {
        if (!toolCalls || toolCalls.length === 0) {
            return '';
        }
        
        let formattedCalls = `
            <div class="tool-calls-section" style="margin-top: 20px; padding-top: 16px; border-top: 2px solid #2C2738;">
                <div class="analysis-section-header" style="background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); color: white; padding: 12px 16px; border-radius: 6px; margin: 16px 0 12px 0; font-weight: 600; font-size: 15px;">
                    🔧 Tool Calls
                </div>
        `;
        
        toolCalls.forEach((call, index) => {
            formattedCalls += `
                <div class="tool-call" style="background: #2C2738; border: 1px solid #3D3A4A; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                    <div class="tool-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span class="tool-name" style="font-weight: 600; font-size: 13px; color: #F9F9F9;">🛠️ ${call.tool_name || 'Unknown Tool'}</span>
                        <span class="tool-status success" style="color: #4CAF50; font-size: 12px;">✅ Success</span>
                    </div>
                    <div class="tool-content" style="font-size: 13px; color: #B2AFC5; background: #1D1A2A; padding: 8px; border-radius: 4px; border: 1px solid #2C2738;">
                        <strong>Parameters:</strong><br>
                        <code style="background: transparent; color: #FFD84D; font-family: 'SF Mono', Monaco, monospace; font-size: 12px;">${JSON.stringify(call.parameters || {}, null, 2)}</code>
                    </div>
                </div>
            `;
        });
        
        formattedCalls += '</div>';
        return formattedCalls;
    }

    /**
     * Convert analysis data to markdown format
     */
    convertToMarkdown(analysisData) {
        if (!analysisData) {
            return '';
        }

        const { type, date, result } = analysisData;

        let markdown = `# ${type}\n\n`;
        markdown += `**Date:** ${new Date(date).toLocaleString()}\n\n`;

        if (typeof result === 'string') {
            markdown += `## Analysis Result\n\n${result}\n`;
        } else if (typeof result === 'object' && result !== null) {
            markdown += `## Analysis Result\n\n`;
            if (result.content) {
                markdown += `${result.content}\n\n`;
            }

            if (result.tool_calls) {
                markdown += this.formatToolCalls(result.tool_calls);
            }

            const otherData = { ...result };
            delete otherData.content;
            delete otherData.tool_calls;

            if (Object.keys(otherData).length > 0) {
                markdown += `### Additional Data\n\n`;
                markdown += `\`\`\`json\n${JSON.stringify(otherData, null, 2)}\n\`\`\`\n`;
            }
        } else {
            markdown += `## Analysis Result\n\n\`\`\`json\n${JSON.stringify(result, null, 2)}\n\`\`\`\n`;
        }

        return markdown;
    }

    /**
     * Copy analysis results to clipboard with clean text formatting
     */
    async copyResults() {
        if (!this.controller.currentAnalysis || !this.controller.currentAnalysis.result) {
            this.controller.uiManager.showError('No results to copy');
            return;
        }
        
        try {
            let content = '';
            const result = this.controller.currentAnalysis.result;
            
            // Extract content from result
            if (typeof result === 'string') {
                content = result;
            } else if (result.content) {
                content = result.content;
            } else {
                content = JSON.stringify(result, null, 2);
            }
            
            // Convert HTML formatting back to clean text
            const cleanText = this.convertHtmlToCleanText(content);
            
            const textToCopy = `${this.controller.currentAnalysis.type}\n${'='.repeat(50)}\n\n${cleanText}`;
            await navigator.clipboard.writeText(textToCopy);
            this.controller.uiManager.showSuccess('Results copied to clipboard!');
        } catch (error) {
            console.error('Copy failed:', error);
            this.controller.uiManager.showError('Failed to copy results');
        }
    }

    /**
     * Convert HTML formatted content back to clean text for copying
     */
    convertHtmlToCleanText(htmlContent) {
        if (typeof htmlContent !== 'string') {
            return htmlContent;
        }
        
        // Create a temporary div to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;
        
        // Extract text content and format it nicely
        let cleanText = tempDiv.textContent || tempDiv.innerText || '';
        
        // If it's already plain text, return as is
        if (!htmlContent.includes('<')) {
            return cleanText;
        }
        
        // Add proper spacing for sections
        cleanText = cleanText.replace(/📊\s*/g, '\n📊 SUMMARY\n');
        cleanText = cleanText.replace(/🔍\s*/g, '\n🔍 KEY INSIGHTS\n');
        cleanText = cleanText.replace(/💡\s*/g, '\n💡 RECOMMENDATIONS\n');
        cleanText = cleanText.replace(/📈\s*/g, '\n📈 MARKET ANALYSIS\n');
        
        // Clean up extra whitespace
        cleanText = cleanText.replace(/\n\s*\n\s*\n/g, '\n\n');
        cleanText = cleanText.replace(/^\s+|\s+$/g, '');
        
        return cleanText;
    }

    /**
     * Export analysis results as markdown file
     */
    exportResults() {
        if (!this.controller.currentAnalysis) {
            this.controller.uiManager.showError('No analysis to export');
            return;
        }

        try {
            const exportData = {
                type: this.controller.currentAnalysis.type,
                date: this.controller.currentAnalysis.timestamp,
                result: this.controller.currentAnalysis.result
            };

            const markdownContent = this.convertToMarkdown(exportData);
            const blob = new Blob([markdownContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-analysis-${Date.now()}.md`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.controller.uiManager.showSuccess('Analysis exported successfully!');
        } catch (error) {
            console.error('Error exporting results:', error);
            this.controller.uiManager.showError('Failed to export analysis');
        }
    }

    /**
     * Load selected prompt from dropdown into custom analysis form
     */
    async loadSelectedPrompt() {
        try {
            const select = document.getElementById('savedPromptsSelect');
            const promptId = select?.value;

            if (!promptId) {
                this.controller.uiManager.showError('Please select a prompt to load');
                return;
            }

            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);

            if (prompt) {
                const customPromptTextarea = document.getElementById('customPrompt');
                if (customPromptTextarea) {
                    customPromptTextarea.value = prompt.content;
                }

                await promptManager.incrementUsage(promptId);
                this.controller.uiManager.showSuccess(`Loaded: ${prompt.title}`);

                // Refresh the dropdown to update usage count
                if (this.controller.promptUIManager) {
                    await this.controller.promptUIManager.loadSavedPromptsSelect();
                }
            } else {
                this.controller.uiManager.showError('Prompt not found');
            }
        } catch (error) {
            console.error('Error loading selected prompt:', error);
            this.controller.uiManager.showError('Failed to load prompt');
        }
    }
}
